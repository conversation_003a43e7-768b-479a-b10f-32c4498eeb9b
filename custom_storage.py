import os
import json
import logging
from typing import Dict, Any, Optional

# Get logger
app_logger = logging.getLogger('app')
error_logger = logging.getLogger('errors')

class FileSystemStorage:
    """
    Custom storage class for Supabase authentication that uses the filesystem.
    This replaces the default MemoryStorage and avoids using flask-storage.
    """

    def __init__(self, storage_dir: str = None):
        """
        Initialize the storage with a directory path.

        Args:
            storage_dir: Directory to store auth data. If None, uses 'instance/auth_storage'.
        """
        if storage_dir is None:
            # Default to instance/auth_storage
            self.storage_dir = os.path.join('instance', 'auth_storage')
        else:
            self.storage_dir = storage_dir

        # Create the storage directory if it doesn't exist
        os.makedirs(self.storage_dir, exist_ok=True)
        app_logger.info(f"Initialized FileSystemStorage at {self.storage_dir}")

        # Keep a memory cache for faster access
        self._cache = {}

    def _get_storage_path(self, key: str) -> str:
        """
        Get the file path for a storage key.

        Args:
            key: The storage key.

        Returns:
            The file path for the key.
        """
        # Sanitize the key to make it safe for use as a filename
        safe_key = key.replace('/', '_').replace('\\', '_').replace(':', '_')
        return os.path.join(self.storage_dir, f"{safe_key}.json")

    def get_item(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get an item from storage.

        Args:
            key: The storage key.

        Returns:
            The stored data or None if not found.
        """
        # Check cache first
        if key in self._cache:
            return self._cache[key]

        file_path = self._get_storage_path(key)

        if not os.path.exists(file_path):
            return None

        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                # Update cache
                self._cache[key] = data
                return data
        except Exception as e:
            error_logger.exception(f"Error reading from storage for key {key}: {str(e)}")
            return None

    def set_item(self, key: str, data: Dict[str, Any]) -> None:
        """
        Store an item.

        Args:
            key: The storage key.
            data: The data to store.
        """
        file_path = self._get_storage_path(key)

        try:
            # Update cache
            self._cache[key] = data

            # Write to file
            with open(file_path, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            error_logger.exception(f"Error writing to storage for key {key}: {str(e)}")

    def remove_item(self, key: str) -> None:
        """
        Remove an item from storage.

        Args:
            key: The storage key.
        """
        file_path = self._get_storage_path(key)

        # Remove from cache
        if key in self._cache:
            del self._cache[key]

        # Remove file if it exists
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except Exception as e:
                error_logger.exception(f"Error removing storage for key {key}: {str(e)}")

    def clear(self) -> None:
        """
        Clear all stored items.
        """
        # Clear cache
        self._cache = {}

        # Remove all files in the storage directory
        try:
            for filename in os.listdir(self.storage_dir):
                if filename.endswith('.json'):
                    os.remove(os.path.join(self.storage_dir, filename))
        except Exception as e:
            error_logger.exception(f"Error clearing storage: {str(e)}")

    def persist_session(self, session_data: Dict[str, Any]) -> None:
        """
        Helper method to persist a session to storage.

        Args:
            session_data: The session data to persist.
        """
        if not session_data:
            app_logger.warning("Empty session data provided to persist_session")
            return

        # Log what we received for debugging
        app_logger.info(f"Persisting session data with keys: {list(session_data.keys())}")

        # Try different possible keys for session ID
        session_id = None
        for id_key in ['id', 'access_token', 'refresh_token']:
            if id_key in session_data and session_data[id_key]:
                session_id = session_data[id_key]
                app_logger.info(f"Using {id_key} as session identifier")
                break

        if session_id:
            # Store with a consistent key format
            key = f"supabase_session_{session_id[:8]}"  # Use first 8 chars of ID/token

            # Store the session data
            self.set_item(key, session_data)

            # Also store with a fixed key for easier retrieval
            self.set_item("current_session", session_data)

            app_logger.info(f"Persisted session to storage with key {key}")
        else:
            # If no ID found, still try to store with a fixed key
            app_logger.warning("No session ID found, storing with fixed key only")
            self.set_item("current_session", session_data)

    def get_current_session(self) -> Optional[Dict[str, Any]]:
        """
        Get the current session data.

        Returns:
            The current session data or None if not found.
        """
        return self.get_item("current_session")
