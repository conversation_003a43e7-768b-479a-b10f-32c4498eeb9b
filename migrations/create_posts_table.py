"""
Migration script to create the posts table.
"""
import sys
import os

# Add the parent directory to the path so we can import models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models import db, Post

def create_posts_table():
    """Create the posts table in the database."""
    app = Flask(__name__)

    # Use the same database URI as the main application
    instance_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'instance')
    os.makedirs(instance_path, exist_ok=True)
    default_db_path = 'sqlite:///' + os.path.join(instance_path, 'database.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', default_db_path)
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    db.init_app(app)

    with app.app_context():
        # Create the posts table
        # Create a specific table using metadata
        Post.__table__.create(db.engine, checkfirst=True)
        print("Posts table created successfully!")

if __name__ == "__main__":
    create_posts_table()
