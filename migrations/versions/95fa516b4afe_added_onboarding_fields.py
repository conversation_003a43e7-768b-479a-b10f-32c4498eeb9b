"""Added onboarding fields

Revision ID: 95fa516b4afe
Revises: 0dd39ef42343
Create Date: 2025-05-25 15:30:13.329067

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '95fa516b4afe'
down_revision = '0dd39ef42343'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('onboarding_completed', sa.<PERSON>(), nullable=False))
        batch_op.add_column(sa.Column('grade_level', sa.String(length=10), nullable=True))
        batch_op.add_column(sa.Column('subjects_taken', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('subject_confidence', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('onboarding_completed_at', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('onboarding_completed_at')
        batch_op.drop_column('subject_confidence')
        batch_op.drop_column('subjects_taken')
        batch_op.drop_column('grade_level')
        batch_op.drop_column('onboarding_completed')

    # ### end Alembic commands ###
