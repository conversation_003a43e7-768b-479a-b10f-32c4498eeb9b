"""create posts table

Revision ID: 1a2b3c4d5e6f
Revises: 
Create Date: 2023-05-17 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1a2b3c4d5e6f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create posts table
    op.create_table('posts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
        sa.Column('group_id', sa.Integer(), nullable=False),
        sa.Column('post_type', sa.String(length=50), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('data', sa.Text(), nullable=True),
        sa.Column('timestamp', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['group_id'], ['groups.id'], name='fk_posts_group_id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='fk_posts_user_id'),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    # Drop posts table
    op.drop_table('posts')
