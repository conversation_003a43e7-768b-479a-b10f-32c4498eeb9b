import os
import sqlite3
from flask_migrate import upgrade

# Import all models to ensure they are registered with SQLAlchemy
# These imports are necessary for db.create_all() to work properly
from models import (
    db, Subject, Topic, User, Question, Part, Option, Attachment,  # noqa: F401
    Group, DailyActivity, DailyActiveTime, Submission, ProblemSet,  # noqa: F401
    ProblemSetSubmission, IncompleteSubmission, MarkingPoint, Post  # noqa: F401
)

from app import app

def check_tables_exist():
    """Check if database tables already exist"""
    with app.app_context():
        try:
            print(f"Checking tables in database: {db.engine.url}")
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            # Define core tables that should exist (based on models.py)
            core_tables = [
                'users', 'subjects', 'topics', 'questions', 'parts',
                'options', 'attachments', 'groups', 'submissions',
                'problemsets', 'marking_points'
            ]

            # Additional tables that might exist
            additional_tables = [
                'daily_activity', 'daily_active_time', 'problemset_submissions',
                'incomplete_submissions', 'posts'
            ]

            if existing_tables:
                print(f"Existing tables found: {', '.join(existing_tables)}")
            else:
                print("No existing tables found")
                return False

            # Check if core tables exist
            missing_core_tables = [table for table in core_tables if table not in existing_tables]

            if missing_core_tables:
                print(f"Missing core tables: {', '.join(missing_core_tables)}")
                return False
            else:
                print("All core tables exist")

                # Check additional tables
                missing_additional = [table for table in additional_tables if table not in existing_tables]
                if missing_additional:
                    print(f"Missing additional tables: {', '.join(missing_additional)}")
                    print("Will create missing tables...")
                    return False
                else:
                    print("All tables exist")
                    return True

        except Exception as e:
            print(f"Error checking existing tables: {e}")
            return False

def create_tables():
    """Create all database tables if they don't exist"""
    with app.app_context():
        try:
            # Debug database connection
            print(f"Files on disk: {os.listdir('/var/data')}\n\n")

            print(f"Database URL: {app.config.get('SQLALCHEMY_DATABASE_URI')}")
            print(f"Database engine: {db.engine}")
            print(f"Database file path: {db.engine.url}")

            # Check if tables already exist
            if check_tables_exist():
                print("Database tables already initialized, skipping creation")
                return

            print("Creating database tables...")
            db.create_all()
            print("Database tables created successfully")

            print(f"Files on disk: {os.listdir('/var/data')}\n\n")

            # Print all created tables for verification
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"Created tables: {', '.join(tables)}")

            # Verify the database file exists and has content
            if 'sqlite' in str(db.engine.url):
                db_path = str(db.engine.url).replace('sqlite:///', '/')
                if db_path.startswith('//'):
                    db_path = db_path[1:]  # Remove extra leading slash
                print(f"Checking SQLite database at: {db_path}")

                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    sqlite_tables = [row[0] for row in cursor.fetchall()]
                    conn.close()
                    print(f"SQLite tables found: {sqlite_tables}")

                    if os.path.exists(db_path):
                        file_size = os.path.getsize(db_path)
                        print(f"Database file size: {file_size} bytes")
                    else:
                        print(f"WARNING: Database file does not exist at {db_path}")

                except Exception as sqlite_error:
                    print(f"Error checking SQLite database: {sqlite_error}")

        except Exception as e:
            print(f"Error creating database tables: {e}")
            raise

def run_migrations():
    """Run database migrations if needed"""
    with app.app_context():
        try:
            # Check if tables already exist
            if check_tables_exist():
                print("Database tables already initialized, skipping migrations")
                return

            print("Running database migrations...")
            upgrade()
            print("Database migration completed successfully")

            # Verify tables were created
            if not check_tables_exist():
                print("Migration completed but some tables are missing, creating them directly...")
                db.create_all()

        except Exception as e:
            print(f"Migration error: {e}")
            # If migrations fail, try creating tables directly
            print("Migrations failed, attempting to create tables directly...")
            create_tables()

if __name__ == "__main__":
    print("Starting database setup...")

    # Check if we're in production
    # if os.getenv('FLASK_ENV') == 'production':
    #     print("Production environment detected - running migrations")
    #     run_migrations()
    # else:
    #     print("Development environment detected - creating tables")
    #     create_tables()

    create_tables()

    print("Database setup completed!")