#!/usr/bin/env python3
"""
Test script for image extraction and integration in pdftomd.py
"""

import os
import logging
import json
import re
from pdftomd import PDFProcessor, update_structured_data_with_answers

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_image_extraction(pdf_path):
    """Test extracting images from a PDF file"""
    try:
        processor = PDFProcessor()
        logger.info(f"Processing PDF: {pdf_path}")
        
        # Upload the PDF file to Mistral
        with open(pdf_path, "rb") as file:
            uploaded_pdf = processor.client.files.upload(
                file={"file_name": os.path.basename(pdf_path), "content": file},
                purpose="ocr"
            )
            
        signed_url = processor.client.files.get_signed_url(file_id=uploaded_pdf.id)
        
        # Process the PDF with OCR
        ocr_response = processor.client.ocr.process(
            model="mistral-ocr-latest",
            document={"type": "document_url", "document_url": signed_url.url},
            include_image_base64=True
        )
        
        logger.info("OCR processing completed successfully")
        
        # Extract images from the OCR response
        images_data = processor._extract_images_from_ocr(ocr_response)
        logger.info(f"Extracted {len(images_data)} images from PDF")
        
        # Log a summary of the images (without the full base64 data)
        image_summary = []
        for img in images_data:
            img_summary = img.copy()
            if 'base64' in img_summary:
                img_summary['base64'] = img_summary['base64'][:50] + '...' if len(img_summary['base64']) > 50 else img_summary['base64']
            image_summary.append(img_summary)
        
        logger.info(f"Image summary: {json.dumps(image_summary)}")
        
        # Save image IDs to a file for reference
        with open('extracted_image_ids.txt', 'w') as f:
            for img in images_data:
                f.write(f"{img.get('id', 'unknown')}\n")
        
        logger.info("Saved image IDs to extracted_image_ids.txt")
        
        return images_data
    
    except Exception as e:
        logger.error(f"Error during image extraction test: {str(e)}", exc_info=True)
        return []

def test_structured_data_with_images(pdf_path, structured_data_path):
    """Test processing a PDF and integrating images with structured data"""
    try:
        processor = PDFProcessor()
        structured_data = processor.process_pdf(pdf_path)
        
        logger.info(f"Processed PDF and generated structured data with {len(structured_data)} questions")
        
        # Check if any of the structured data contains image references
        image_count = 0
        img_ref_pattern = r'<img src="data:image/'
        
        for question in structured_data:
            desc = question.get('description', '')
            if desc and re.search(img_ref_pattern, desc):
                image_count += 1
            
            for part in question.get('parts', []):
                part_desc = part.get('description', '')
                if part_desc and re.search(img_ref_pattern, part_desc):
                    image_count += 1
                
                for mp in part.get('marking_points', []):
                    mp_desc = mp.get('description', '')
                    if mp_desc and re.search(img_ref_pattern, mp_desc):
                        image_count += 1
        
        logger.info(f"Found {image_count} image references in structured data")
        
        # Save the structured data to the specified path
        if structured_data_path:
            with open(structured_data_path, 'w') as f:
                json.dump(structured_data, f, indent=2)
            logger.info(f"Saved structured data with images to {structured_data_path}")
        
        return structured_data
    
    except Exception as e:
        logger.error(f"Error during structured data test: {str(e)}", exc_info=True)
        return []

def test_answer_extraction_with_images(question_pdf_path, answer_pdf_path, structured_data_path):
    """Test extracting answers with images and updating structured data"""
    try:
        processor = PDFProcessor()
        
        # Process the answer PDF to extract answers with images
        with open(answer_pdf_path, "rb") as file:
            uploaded_pdf = processor.client.files.upload(
                file={"file_name": os.path.basename(answer_pdf_path), "content": file},
                purpose="ocr"
            )
            
        signed_url = processor.client.files.get_signed_url(file_id=uploaded_pdf.id)
        
        ocr_response = processor.client.ocr.process(
            model="mistral-ocr-latest",
            document={"type": "document_url", "document_url": signed_url.url},
            include_image_base64=True
        )
        
        logger.info("OCR processing of answer PDF completed successfully")
        
        # Extract answers from the OCR response
        answers_data = processor._extract_answers_from_ocr(ocr_response)
        
        if not answers_data:
            logger.warning("No answers extracted from OCR response")
            return False
        
        logger.info(f"Extracted {len(answers_data)} questions with answers")
        
        # Update the structured data with the extracted answers
        update_structured_data_with_answers(structured_data_path, answers_data)
        logger.info("Updated structured data with answers and images")
        
        return True
    
    except Exception as e:
        logger.error(f"Error during answer extraction test: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    import argparse
    import re
    
    parser = argparse.ArgumentParser(description="Test image extraction and integration")
    parser.add_argument("--pdf", help="Path to a PDF file for testing image extraction")
    parser.add_argument("--question-pdf", help="Path to a question PDF file")
    parser.add_argument("--answer-pdf", help="Path to an answer PDF file")
    parser.add_argument("--output", help="Path to save the output structured data")
    parser.add_argument("--test", choices=["extract", "structured", "answers", "all"], 
                        default="all", help="Which test to run")
    
    args = parser.parse_args()
    
    # Set default output file name if not provided
    if not args.output and args.pdf:
        args.output = f"test_output_{os.path.basename(args.pdf).replace('.pdf', '.json')}"
    elif not args.output:
        args.output = "test_output.json"
    
    if args.test in ["extract", "all"] and args.pdf:
        test_image_extraction(args.pdf)
    
    if args.test in ["structured", "all"] and args.pdf:
        test_structured_data_with_images(args.pdf, args.output)
    
    if args.test in ["answers", "all"] and args.question_pdf and args.answer_pdf:
        test_answer_extraction_with_images(args.question_pdf, args.answer_pdf, args.output)
    
    logger.info("Tests completed successfully") 