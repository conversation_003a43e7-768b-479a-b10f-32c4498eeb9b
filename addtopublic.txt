-- SQL command to add all users to the group with id=2

INSERT INTO user_group_association (user_id, group_id)
SELECT
    u.id AS user_id,
    2 AS group_id -- The target group ID is 2
FROM
    users u
WHERE
    -- Check if the user is NOT already in the user_group_association table for this group
    NOT EXISTS (
        SELECT 1
        FROM user_group_association uga
        WHERE uga.user_id = u.id
          AND uga.group_id = 2
    );
