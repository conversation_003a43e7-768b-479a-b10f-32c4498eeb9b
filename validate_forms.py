def check_digit(x):
    if isinstance(x, str):
        return x.isdigit() and int(x) > 0
    if isinstance(x, int):
        return x >= 0
    return False

def validate_ocr_review_form(data):
    """
    CHECKS
    1. Score of all parts are integers >= 0
    2. Sum of score of marking points, if generated, = score of part
    3. Marking points, if generated, are non empty

    WARNINGS
    1. Empty topic field
    2. Empty source
    3. No marking points
    """
    warnings = []
    errors = []
    exist_error = False

    for question_id, question in enumerate(data['question_data']):
        errors.append([])
        warnings.append([])
        if not data['topic_id'][question_id]:
            warnings[question_id].append({"message": f"Topic field is empty (Question {question_id+1})",
                                          "id": "topic_id"})
        if not data['source'][question_id]:
            warnings[question_id].append({"message": f"Source field is empty (Question {question_id+1})",
                                          "id": "source"})

        for part_id, part in enumerate(question):
            accum_score = 0
            if 'marking_points' in part and len(part['marking_points']):
                for mp_id, mp in enumerate(part['marking_points']):
                    if not check_digit(mp['score']):
                        errors[question_id].append({"message": f"Marking point score must be a non-negative integer (Question {question_id+1}, Part {part_id+1}, Point {mp_id+1})",
                                                    "id": f"marking-points-{part_id}-{mp_id}-score"})
                        exist_error = True
                    else:
                        accum_score += int(mp['score'])

                    if len(mp['description']) == 0:
                        warnings[question_id].append({"message": f"Marking point description is empty (Question {question_id+1}, Part {part_id+1}, Point {mp_id+1})",
                                                    "id": f"marking-points-{part_id}-{mp_id}-description"})
            else:
                warnings[question_id].append({"message": f"No marking points generated for this part (Question {question_id+1}, Part {part_id+1})",
                                            "id": f"marking-points-container-{part_id}"})
            
            if not check_digit(part['score']):
                errors[question_id].append({"message": f"Part score must be a non-negative integer (Part {part_id+1})",
                                            "id": f"score-{part_id}"})
                exist_error = True
            elif accum_score != int(part['score']):
                errors[question_id].append({"message": f"Sum of marking point scores ({accum_score}) does not equal part score ({part['score']}) (Question {question_id+1}, Part {part_id+1})",
                                                "id": f"marking-points-container-{part_id}"})
                exist_error = True

    return errors, warnings, exist_error