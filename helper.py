def process_csv_file(file_path, topic_id):
    """Process a CSV file and return a list of questions with their parts"""
    questions = []
    current_question = None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as csv_file:
            # First read the header row to get the field names
            csv_reader = csv.reader(csv_file)
            header = next(csv_reader)
            
            # Reset file pointer to beginning
            csv_file.seek(0)
            
            # Skip the header row now that we have it
            next(csv_reader)
            
            for row_values in csv_reader:
                # Create a dictionary from the row values and header
                row = {header[i]: value for i, value in enumerate(row_values) if i < len(header)}
                
                # Check if this is an empty row (all values are empty)
                is_empty = all(not (value.strip() if value else False) for value in row_values)
                
                if is_empty:
                    # If we have a current question, add it to the list and reset
                    if current_question:
                        questions.append(current_question)
                        current_question = None
                    continue
                
                # If this is the first row or a new question after an empty row
                if not current_question:
                    current_question = {
                        'title': row.get('question_title', ''),
                        'description': row.get('question_description', ''),
                        'source': row.get('question_source', ''),
                        'parts': []
                    }
                
                # Process marking points if provided
                marking_points = []
                if row.get('marking_points'):
                    for item in row['marking_points'].split(';'):
                        if ':' in item:
                            desc, score = item.split(':', 1)
                            try:
                                score = int(score.strip())
                                marking_points.append({
                                    'description': desc.strip(),
                                    'score': score
                                })
                            except ValueError:
                                app.logger.warning(f"Invalid score format in marking point: {item}")
                
                # Add this part to the current question
                current_question['parts'].append({
                    'description': row.get('part_description', ''),
                    'answer': row.get('part_answer', ''),
                    'score': int(row.get('part_score', 0)),
                    'marking_points': marking_points
                })
                        
        # Add the last question if it exists
        if current_question:
            questions.append(current_question)
            
        return questions
    except Exception as e:
        app.logger.error(f"Error processing CSV file: {str(e)}")
        raise

def process_excel_file(file_path, topic_id):
    """Process an Excel file and return a list of questions with their parts"""
    questions = []
    current_question = None
    
    try:
        # Load the workbook and first sheet
        wb = openpyxl.load_workbook(file_path)
        ws = wb.active
        
        # Get headers from the first row
        headers = [cell.value for cell in ws[1]]
        
        # Process each row
        for row in ws.iter_rows(min_row=2, values_only=True):
            # Create a dictionary from the row values
            row_data = dict(zip(headers, row))
            
            # Check if this is an empty row (no title, description, or part description)
            is_empty = not any(str(row_data.get(key, '')).strip() for key in ['question_title', 'question_description', 'part_description'])
            
            if is_empty:
                if current_question:
                    questions.append(current_question)
                    current_question = None
                continue
            
            # If this is the first row or a new question after an empty row
            if not current_question:
                current_question = {
                    'title': row_data.get('question_title', ''),
                    'description': row_data.get('question_description', ''),
                    'source': row_data.get('question_source', ''),
                    'parts': []
                }
            
            # Process marking points if provided
            marking_points = []
            if row_data.get('marking_points'):
                mp_items = row_data['marking_points'].split(';')
                for mp in mp_items:
                    if ':' in mp:
                        desc, score = mp.split(':', 1)
                        try:
                            score = int(score)
                            marking_points.append({
                                'description': desc.strip(),
                                'score': score
                            })
                        except ValueError:
                            app.logger.warning(f"Invalid marking point score: {mp}")
            
            # Add the part to the current question
            current_question['parts'].append({
                'description': row_data.get('part_description', ''),
                'answer': row_data.get('part_answer', ''),
                'score': int(row_data.get('part_score', 0)),
                'marking_points': marking_points
            })
        
        # Add the last question if there is one
        if current_question:
            questions.append(current_question)
    
    except Exception as e:
        app.logger.error(f"Error processing Excel file: {str(e)}")
        raise Exception(f"Error processing Excel file: {str(e)}")
    
    return questions