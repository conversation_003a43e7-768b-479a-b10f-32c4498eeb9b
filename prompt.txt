can you change the prompt for bulk_upload, such that when doing extraction, the model stops putting the img md tags in the output? eg ![img-id]. because it is useless, we are using another way. 

can you change the edit_question route, to be the same as the /admin route to upload question, whereby we can also upload mcq options and answers?

DO NOT ASSUME ANYTHING! if you are not sure, ask me to clarify.

use pm2 restart 0 to restart server. changes will be at vastlearn.space