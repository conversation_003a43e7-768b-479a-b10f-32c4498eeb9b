// Confetti animation for first login of the day
document.addEventListener('DOMContentLoaded', function() {
    // Check if we should show confetti (set by the server)
    const showConfetti = document.body.dataset.showConfetti === 'true';

    if (showConfetti) {
        console.log('Showing first login of the day confetti!');
        triggerConfetti();

        // Clear the flag after showing confetti
        document.body.dataset.showConfetti = 'false';

        // Clear the session flag
        fetch('/clear_confetti', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            console.log('Confetti flag cleared:', data);
        })
        .catch(error => {
            console.error('Error clearing confetti flag:', error);
        });
    }
});

function triggerConfetti() {
    // Load canvas-confetti library if not already loaded
    if (typeof confetti === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js';
        script.onload = function() {
            // Once loaded, trigger the confetti
            fireConfetti();
        };
        document.head.appendChild(script);
    } else {
        // If already loaded, trigger immediately
        fireConfetti();
    }
}

function fireConfetti() {
    // Create a celebratory message
    showCelebrationMessage();

    // Fire the confetti
    const duration = 3 * 1000;
    const end = Date.now() + duration;

    // First burst
    confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
    });

    // Confetti cannon effect
    (function frame() {
        confetti({
            particleCount: 2,
            angle: 60,
            spread: 55,
            origin: { x: 0 }
        });

        confetti({
            particleCount: 2,
            angle: 120,
            spread: 55,
            origin: { x: 1 }
        });

        if (Date.now() < end) {
            requestAnimationFrame(frame);
        }
    }());
}

function showCelebrationMessage() {
    // Create a celebration message element
    const messageEl = document.createElement('div');
    messageEl.className = 'celebration-message';
    messageEl.innerHTML = `
        <div class="celebration-content">
            <h3>Welcome Back!</h3>
            <p>First login of the day! 🎉</p>
        </div>
    `;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
        .celebration-message {
            position: fixed;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            animation: celebration-fade 4s forwards;
            text-align: center;
        }

        .celebration-content h3 {
            margin: 0 0 10px 0;
            color: #4f46e5;
            font-size: 1.5rem;
        }

        .celebration-content p {
            margin: 0;
            font-size: 1.1rem;
        }

        @keyframes celebration-fade {
            0% { opacity: 0; transform: translate(-50%, 20px); }
            10% { opacity: 1; transform: translate(-50%, 0); }
            80% { opacity: 1; transform: translate(-50%, 0); }
            100% { opacity: 0; transform: translate(-50%, -20px); }
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(messageEl);

    // Remove the message after animation completes
    setTimeout(() => {
        messageEl.remove();
        style.remove();
    }, 4000);
}
