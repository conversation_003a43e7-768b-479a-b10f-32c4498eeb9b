import os
from flask import Blueprint, send_from_directory, current_app, abort

# Create a Blueprint for file serving
serve_bp = Blueprint('serve', __name__)

@serve_bp.route('/attachment/<filename>', methods=['GET'])
def serve_file(filename):
    """Serve uploaded files"""
    upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')

    # Security check - validate filename to prevent directory traversal attacks
    if '..' in filename or filename.startswith('/'):
        abort(404)

    # Check if file exists
    if not os.path.exists(os.path.join(upload_folder, filename)):
        abort(404)

    return send_from_directory(upload_folder, filename)
