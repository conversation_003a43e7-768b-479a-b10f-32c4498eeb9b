import os
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import render_template, request, redirect, url_for, flash, session, jsonify
from sqlalchemy.orm import joinedload

from models import db, ProblemSet, Question, Group, User, ProblemSetSubmission, IncompleteSubmission, Submission, Part, user_group_association, problemset_group_association
from .utils import login_required, error_logger, app_logger, allowed_file, ALLOWED_ATTACHMENTS # Import helpers

def register_problemset_routes(app, db, session):
    # Get upload folder from app config
    upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')

    def check_problemset_access(problemset_id, user_id):
        """Helper function to check if a user has access to a problem set."""
        problemset = ProblemSet.query.options(
            joinedload(ProblemSet.shared_with) # Eager load shared groups
        ).get(problemset_id)

        if not problemset:
            return None, "Problem set not found"

        # Check if user is the creator
        if problemset.created_by == user_id:
            return problemset, None # Access granted

        # Check if user is in any group the problem set is shared with
        user_groups = set(g.id for g in User.query.get(user_id).groups)
        problemset_groups = set(g.id for g in problemset.shared_with)
        if user_groups & problemset_groups: # Check for intersection
            return problemset, None # Access granted

        return None, "You do not have access to this problem set"


    @app.route("/problemsets")
    @login_required
    def list_problemsets():
        """Lists problem sets owned by or shared with the user."""
        user_id = session['user_id']
        try:
            # Get problem sets created by the user
            owned_problemsets = ProblemSet.query.filter_by(created_by=user_id)\
                                             .order_by(ProblemSet.created_at.desc()).all()

            # Get problem sets shared with user's groups (using association table)
            shared_problemsets = ProblemSet.query.join(
                problemset_group_association
            ).join(
                Group
            ).join(
                user_group_association
            ).filter(
                user_group_association.c.user_id == user_id,
                ProblemSet.created_by != user_id # Exclude owned ones from shared list
            ).order_by(ProblemSet.created_at.desc()).distinct().all() # Use distinct to avoid duplicates if shared via multiple groups

            return render_template(
                "problemsets/list.html",
                owned_problemsets=owned_problemsets,
                shared_problemsets=shared_problemsets
            )
        except Exception as e:
            error_logger.exception(f"Error listing problem sets for user {user_id}: {e}")
            flash("Error loading problem sets.", "error")
            return redirect(url_for('index'))


    @app.route("/problemsets/create", methods=['GET', 'POST'])
    @login_required
    def create_problemset():
        """Handles creation of a new problem set."""
        user_id = session['user_id']

        if request.method == 'POST':
            name = request.form.get('name')
            description = request.form.get('description', '')
            question_ids = request.form.getlist('questions') # List of question IDs
            group_ids = request.form.getlist('groups') # List of group IDs to share with

            # --- Validation ---
            if not name:
                flash('Problem set name is required.', 'error')
                # Repopulate form data for rendering
                questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
                groups = Group.query.order_by(Group.name).all()
                return render_template("problemsets/create.html", questions=questions, groups=groups, name=name, description=description, selected_questions=question_ids, selected_groups=group_ids), 400

            if not question_ids:
                flash('At least one question must be selected.', 'error')
                questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
                groups = Group.query.order_by(Group.name).all()
                return render_template("problemsets/create.html", questions=questions, groups=groups, name=name, description=description, selected_questions=question_ids, selected_groups=group_ids), 400
            # --- End Validation ---

            # Handle PDF attachment if provided
            pdf_attachment_filename = None
            if 'pdf_attachment' in request.files:
                pdf_file = request.files['pdf_attachment']
                if pdf_file and pdf_file.filename != '':
                    if allowed_file(pdf_file.filename, ALLOWED_ATTACHMENTS):
                        # Create a secure filename with timestamp
                        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                        pdf_attachment_filename = secure_filename(f"problemset_{timestamp}_{pdf_file.filename}")

                        # Ensure upload directory exists
                        os.makedirs(upload_folder, exist_ok=True)

                        # Save the file
                        pdf_file.save(os.path.join(upload_folder, pdf_attachment_filename))
                        app_logger.info(f"Saved PDF attachment for problemset: {pdf_attachment_filename}")
                    else:
                        flash('Only PDF files are allowed for attachments.', 'error')
                        questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
                        groups = Group.query.order_by(Group.name).all()
                        return render_template("problemsets/create.html", questions=questions, groups=groups, name=name, description=description, selected_questions=question_ids, selected_groups=group_ids), 400

            try:
                problemset = ProblemSet(
                    name=name,
                    description=description,
                    created_by=user_id,
                    pdf_attachment=pdf_attachment_filename
                )

                # Add selected questions (validate IDs)
                valid_question_ids = [int(qid) for qid in question_ids if qid.isdigit()]
                questions = Question.query.filter(Question.id.in_(valid_question_ids)).all()
                if len(questions) != len(valid_question_ids):
                     flash("Some selected questions were invalid.", "warning")
                problemset.questions.extend(questions)

                # Add selected groups to share with (validate IDs)
                if group_ids:
                    valid_group_ids = [int(gid) for gid in group_ids if gid.isdigit()]
                    # Ensure user has permission to share with these groups? (e.g., is owner or member?) - Optional
                    groups = Group.query.filter(Group.id.in_(valid_group_ids)).all()
                    if len(groups) != len(valid_group_ids):
                         flash("Some selected groups for sharing were invalid.", "warning")
                    problemset.shared_with.extend(groups)

                db.session.add(problemset)
                db.session.commit()

                flash(f'Problem set "{name}" created successfully.', 'success')
                app_logger.info(f"User {session.get('username')} created problem set '{name}' (ID: {problemset.id})")
                return redirect(url_for('list_problemsets')) # Redirect to list view

            except Exception as e:
                db.session.rollback()
                error_logger.exception(f"Error creating problem set '{name}' for user {user_id}: {e}")
                flash(f'Error creating problem set: {str(e)}', 'error')
                # Repopulate form on error
                questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
                groups = Group.query.order_by(Group.name).all()
                return render_template("problemsets/create.html", questions=questions, groups=groups, name=name, description=description, selected_questions=question_ids, selected_groups=group_ids), 500

        # GET request - show creation form
        # Eager load parts to ensure first part's description is available for tooltips
        questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
        # Only show groups the user is a member of or owns for sharing?
        user = User.query.get(user_id)
        available_groups = user.groups # Or query all groups if admin can share with any group
        return render_template(
            "problemsets/create.html",
            questions=questions,
            groups=available_groups # Pass appropriate groups
        )


    @app.route("/problemsets/<int:id>")
    @login_required
    def view_problemset(id):
        """Displays a specific problem set if user has access."""
        user_id = session['user_id']
        problemset, error_msg = check_problemset_access(id, user_id)

        if error_msg:
            flash(error_msg, 'error')
            return redirect(url_for('list_problemsets'))

        # Get the subject name from the first question with a topic, or use a default
        subject_name = "Unknown Subject"
        for question in problemset.questions:
            if question.topic:
                subject_name = question.topic.subject.name
                break

        return render_template(
            "problemsets/view.html",
            problemset=problemset,
            subject_name=subject_name
        )


    @app.route("/problemsets/<int:id>/edit", methods=['GET', 'POST'])
    @login_required
    def edit_problemset(id):
        """Handles editing of a problem set (only creator)."""
        user_id = session['user_id']
        problemset = ProblemSet.query.get_or_404(id)

        # Check if user is the creator
        if problemset.created_by != user_id:
            flash('You can only edit problem sets you created.', 'error')
            return redirect(url_for('list_problemsets'))

        if request.method == 'POST':
            name = request.form.get('name')
            description = request.form.get('description', '')
            question_ids = request.form.getlist('questions')
            group_ids = request.form.getlist('groups')
            remove_pdf = request.form.get('remove_pdf') == 'on'

            # --- Validation ---
            if not name:
                flash('Problem set name is required.', 'error')
                # Repopulate form data for rendering
                questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
                groups = Group.query.order_by(Group.name).all() # Or user's groups
                return render_template("problemsets/edit.html", problemset=problemset, questions=questions, groups=groups, name=name, description=description, selected_questions=question_ids, selected_groups=group_ids), 400

            if not question_ids:
                flash('At least one question must be selected.', 'error')
                questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
                groups = Group.query.order_by(Group.name).all() # Or user's groups
                return render_template("problemsets/edit.html", problemset=problemset, questions=questions, groups=groups, name=name, description=description, selected_questions=question_ids, selected_groups=group_ids), 400
            # --- End Validation ---

            # Handle PDF attachment if provided or if removal requested
            if remove_pdf and problemset.pdf_attachment:
                # Optionally delete the file from disk
                # pdf_path = os.path.join(upload_folder, problemset.pdf_attachment)
                # if os.path.exists(pdf_path):
                #     os.remove(pdf_path)
                problemset.pdf_attachment = None
                app_logger.info(f"Removed PDF attachment from problemset {problemset.id}")

            if 'pdf_attachment' in request.files:
                pdf_file = request.files['pdf_attachment']
                if pdf_file and pdf_file.filename != '':
                    if allowed_file(pdf_file.filename, ALLOWED_ATTACHMENTS):
                        # Create a secure filename with timestamp
                        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                        pdf_attachment_filename = secure_filename(f"problemset_{timestamp}_{pdf_file.filename}")

                        # Ensure upload directory exists
                        os.makedirs(upload_folder, exist_ok=True)

                        # Save the file
                        pdf_file.save(os.path.join(upload_folder, pdf_attachment_filename))

                        # Update the problemset with the new filename
                        problemset.pdf_attachment = pdf_attachment_filename
                        app_logger.info(f"Updated PDF attachment for problemset {problemset.id}: {pdf_attachment_filename}")
                    else:
                        flash('Only PDF files are allowed for attachments.', 'error')
                        questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
                        groups = Group.query.order_by(Group.name).all()
                        return render_template("problemsets/edit.html", problemset=problemset, questions=questions, groups=groups, name=name, description=description, selected_questions=question_ids, selected_groups=group_ids), 400

            try:
                problemset.name = name
                problemset.description = description

                # Update questions (replace existing)
                valid_question_ids = [int(qid) for qid in question_ids if qid.isdigit()]
                questions = Question.query.filter(Question.id.in_(valid_question_ids)).all()
                if len(questions) != len(valid_question_ids):
                     flash("Some selected questions were invalid.", "warning")
                problemset.questions = questions # Replace the list

                # Update shared groups (replace existing)
                valid_group_ids = [int(gid) for gid in group_ids if gid.isdigit()]
                # Ensure user has permission to share with these groups? (Optional)
                groups = Group.query.filter(Group.id.in_(valid_group_ids)).all()
                if len(groups) != len(valid_group_ids):
                     flash("Some selected groups for sharing were invalid.", "warning")
                problemset.shared_with = groups # Replace the list

                db.session.commit()
                flash(f'Problem set "{name}" updated successfully.', 'success')
                app_logger.info(f"User {session.get('username')} updated problem set '{name}' (ID: {id})")
                return redirect(url_for('view_problemset', id=id)) # Redirect to view

            except Exception as e:
                db.session.rollback()
                error_logger.exception(f"Error updating problem set {id} for user {user_id}: {e}")
                flash(f'Error updating problem set: {str(e)}', 'error')
                # Repopulate form on error
                questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
                groups = Group.query.order_by(Group.name).all() # Or user's groups
                return render_template("problemsets/edit.html", problemset=problemset, questions=questions, groups=groups, name=name, description=description, selected_questions=question_ids, selected_groups=group_ids), 500

        # GET request - show edit form
        # Eager load parts to ensure first part's description is available for tooltips
        questions = Question.query.options(joinedload(Question.parts)).order_by(Question.title).all()
        # Only show groups the user is a member of or owns for sharing?
        user = User.query.get(user_id)
        available_groups = user.groups # Or query all groups
        return render_template(
            "problemsets/edit.html",
            problemset=problemset,
            questions=questions,
            groups=available_groups, # Pass appropriate groups
            # Pass selected IDs for the form
            selected_questions=[q.id for q in problemset.questions],
            selected_groups=[g.id for g in problemset.shared_with]
        )


    @app.route("/problemsets/<int:id>/delete", methods=['POST'])
    @login_required
    def delete_problemset(id):
        """Deletes a problem set (only creator)."""
        user_id = session['user_id']
        problemset = ProblemSet.query.get_or_404(id)

        # Check if user is the creator
        if problemset.created_by != user_id:
            flash('You can only delete problem sets you created.', 'error')
            return redirect(url_for('list_problemsets'))

        try:
            ps_name = problemset.name # Get name before deleting
            # Handle related data:
            # - Delete associated ProblemSetSubmissions?
            # - Delete associated IncompleteSubmissions?
            # SQLAlchemy cascade rules might handle this if set up in models.
            # Explicitly delete if cascade is not set:
            ProblemSetSubmission.query.filter_by(problemset_id=id).delete()
            IncompleteSubmission.query.filter_by(problemset_id=id).delete()
            # Note: Deleting the ProblemSet might automatically handle relationships
            # via association tables depending on cascade settings.

            db.session.delete(problemset)
            db.session.commit()
            flash(f'Problem set "{ps_name}" deleted successfully.', 'success')
            app_logger.info(f"User {session.get('username')} deleted problem set '{ps_name}' (ID: {id})")
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error deleting problem set {id} by user {user_id}: {e}")
            flash(f'Error deleting problem set: {str(e)}', 'error')

        return redirect(url_for('list_problemsets'))


    @app.route("/problemsets/<int:id>/do")
    @login_required
    def do_problemset(id):
        """Displays the interface for working on a problem set."""
        user_id = session['user_id']
        problemset, error_msg = check_problemset_access(id, user_id)

        if error_msg:
            flash(error_msg, 'error')
            return redirect(url_for('list_problemsets'))

        # Eager load questions and their parts for efficiency
        problemset = ProblemSet.query.options(
            joinedload(ProblemSet.questions).joinedload(Question.parts)
        ).get(id)

        # Get past submissions for this user and problemset
        past_submissions = ProblemSetSubmission.query.filter_by(
            problemset_id=id,
            user_id=user_id
        ).order_by(ProblemSetSubmission.submitted_at.desc()).all()

        # Create a mapping of part_id to the latest submission for that part
        part_submissions = {}
        for submission in past_submissions:
            for question_submission in submission.question_submissions:
                part_id = question_submission.part_id
                if part_id not in part_submissions:
                    part_submissions[part_id] = question_submission

        return render_template(
            "problemsets/do.html",
            problemset=problemset,
            past_submissions=past_submissions,
            part_submissions=part_submissions
        )


    @app.route("/problemsets/<int:id>/submissions")
    @login_required
    def problemset_submissions(id):
        """View all submissions for a specific problem set"""
        problemset = ProblemSet.query.get_or_404(id)

        # Check if user has access
        user_id = session['user_id']
        if problemset.created_by != user_id:
            user_groups = set(g.id for g in User.query.get(user_id).groups)
            problemset_groups = set(g.id for g in problemset.shared_with)
            if not user_groups & problemset_groups:  # If no groups in common
                flash('You do not have access to this problem set', 'error')
                return redirect(url_for('list_problemsets'))

        # Get all submissions for this problemset
        submissions = ProblemSetSubmission.query.filter_by(
            problemset_id=id
        ).order_by(ProblemSetSubmission.submitted_at.desc()).all()

        return render_template(
            "problemsets/submissions.html",
            problemset=problemset,
            submissions=submissions
        )


    @app.route("/problemsets/submissions/<int:submission_id>")
    @login_required
    def problemset_submission_details(submission_id):
        """View details of a specific problemset submission"""
        submission = ProblemSetSubmission.query.get_or_404(submission_id)

        # Check if user has access
        user_id = session['user_id']
        if submission.problemset.created_by != user_id:
            user_groups = set(g.id for g in User.query.get(user_id).groups)
            problemset_groups = set(g.id for g in submission.problemset.shared_with)
            if not user_groups & problemset_groups:  # If no groups in common
                flash('You do not have access to this submission', 'error')
                return redirect(url_for('list_problemsets'))

        return render_template(
            "problemsets/submission_details.html",
            submission=submission
        )
