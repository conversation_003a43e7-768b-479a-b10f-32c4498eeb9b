import os
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename

# Create a Blueprint for attachment handling
attachment_bp = Blueprint('attachment', __name__)

# Define allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

def allowed_file(filename):
    """Check if the file extension is allowed"""
    return '.' in filename and \
           filename.split('.')[-1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(original_filename):
    """Generate a unique filename for uploaded files"""
    # Get the file extension
    ext = original_filename.split('.')[-1].lower()
    
    # Create a unique filename with timestamp and UUID
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S%f')
    unique_id = str(uuid.uuid4().hex)[:8]
    
    return f"img-{unique_id}_{timestamp}.{ext}"

@attachment_bp.route('/upload_attachment', methods=['POST'])
def upload_attachment():
    """Handle file upload and return the path for embedding in the question"""
    if 'file' not in request.files:
        return jsonify({
            'success': False,
            'message': 'No file part in the request'
        }), 400
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({
            'success': False,
            'message': 'No file selected'
        }), 400
    
    if file and allowed_file(file.filename):
        # Create a secure, unique filename
        filename = generate_unique_filename(secure_filename(file.filename))
        
        # Ensure upload directory exists
        upload_dir = os.path.join(current_app.static_folder, 'uploads', 'attachments')
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save the file
        file_path = os.path.join(upload_dir, filename)
        file.save(file_path)
        
        # Return the file path to be embedded in the question
        serve_path = f"/serve/attachment/{filename}"
        
        return jsonify({
            'success': True,
            'file_path': serve_path,
            'filename': filename
        })
    
    return jsonify({
        'success': False,
        'message': 'File type not allowed. Allowed types: ' + ', '.join(ALLOWED_EXTENSIONS)
    }), 400 