from flask import session, flash, redirect, url_for, render_template
from datetime import datetime, timedelta
import json
from sqlalchemy import func, distinct
from models import db, User, Question, Subject, Topic, Submission, Part
from .utils import login_required, error_logger

def register_dashboard_routes(app, db, session):

    @app.route("/dashboard")
    @login_required
    def dashboard():
        """Display the user dashboard with personalized content"""
        user_id = session.get('user_id')
        user = User.query.get(user_id)

        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('login'))

        # If user hasn't completed onboarding, redirect to onboarding
        if not (user.onboarding_completed is True):
            return redirect(url_for('onboarding'))

        try:
            # Get recommended problems based on user's subjects
            recommended_problems = get_recommended_problems(user)

            # Get user statistics
            user_stats = get_user_statistics(user)

            return render_template('dashboard.html',
                                 user=user,
                                 recommended_problems=recommended_problems,
                                 user_stats=user_stats)

        except Exception as e:
            error_logger.exception(f"Error loading dashboard for user {user_id}: {str(e)}")
            flash('An error occurred while loading your dashboard.', 'error')
            return redirect(url_for('vault'))

def get_recommended_problems(user, limit=10):
    """Get recommended problems based on user's subjects and confidence levels"""
    try:
        user_subjects = user.get_subjects_taken()
        user_confidence = user.get_subject_confidence()

        if not user_subjects:
            return []

        # Map onboarding subject codes to database subject IDs
        subject_ids = map_onboarding_subjects_to_db_ids(user_subjects)

        if not subject_ids:
            return []

        # Get questions from user's subjects
        query = Question.query.join(Topic).join(Subject).filter(
            Subject.id.in_(subject_ids)
        )

        # Exclude questions the user has already attempted
        attempted_question_ids = db.session.query(Submission.question_id).filter(
            Submission.user_id == user.id
        ).distinct()

        query = query.filter(~Question.id.in_(attempted_question_ids))

        # Order by topic diversity and limit results
        recommended_questions = query.order_by(func.random()).limit(limit).all()

        # Add additional metadata for display
        for question in recommended_questions:
            question.subject_name = question.topic.subject.name if question.topic and question.topic.subject else "Unknown"
            question.topic_name = question.topic.name if question.topic else "Unknown"

            # Get confidence level for this subject
            subject_code = map_db_subject_to_onboarding_code(question.topic.subject.name)
            question.user_confidence = user_confidence.get(subject_code, 3) if subject_code else 3

        return recommended_questions

    except Exception as e:
        error_logger.exception(f"Error getting recommended problems for user {user.id}: {str(e)}")
        return []

def map_onboarding_subjects_to_db_ids(onboarding_subjects):
    """Map onboarding subject codes to database subject IDs"""
    try:
        # Load the subject mapping from the JSON file
        import os
        json_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'content', 'subject_data.json')

        with open(json_path, 'r') as f:
            subject_data = json.load(f)

        # Create mapping from subject name to syllabus
        subject_name_to_syllabus = {
            subject['name']: subject['syllabus']
            for subject in subject_data['subjects']
        }

        # Get syllabus codes for user's subjects
        syllabus_codes = []
        for subject_code in onboarding_subjects:
            if subject_code in subject_name_to_syllabus:
                syllabus_codes.append(subject_name_to_syllabus[subject_code])

        # Get database subject IDs
        if syllabus_codes:
            subjects = Subject.query.filter(Subject.syllabus.in_(syllabus_codes)).all()
            return [subject.id for subject in subjects]

        return []

    except Exception as e:
        error_logger.exception(f"Error mapping onboarding subjects to DB IDs: {str(e)}")
        return []

def map_db_subject_to_onboarding_code(db_subject_name):
    """Map database subject name back to onboarding code"""
    try:
        # Load the subject mapping from the JSON file
        import os
        json_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'content', 'subject_data.json')

        with open(json_path, 'r') as f:
            subject_data = json.load(f)

        # Find the subject code that matches the database subject name
        for subject in subject_data['subjects']:
            if subject['name'] == db_subject_name:
                return subject['name']

        return None

    except Exception as e:
        error_logger.exception(f"Error mapping DB subject to onboarding code: {str(e)}")
        return None

def get_user_statistics(user):
    """Get user statistics for the dashboard"""
    try:
        # Get total problems solved (distinct questions with submissions)
        problems_solved = db.session.query(func.count(func.distinct(Submission.question_id))).filter(
            Submission.user_id == user.id
        ).scalar() or 0

        # Get total problems available to the user based on their subjects
        user_subjects = user.get_subjects_taken()
        if user_subjects:
            subject_ids = map_onboarding_subjects_to_db_ids(user_subjects)
            if subject_ids:
                total_problems = db.session.query(func.count(Question.id)).join(Topic).join(Subject).filter(
                    Subject.id.in_(subject_ids)
                ).scalar() or 0
            else:
                total_problems = 0
        else:
            total_problems = 0

        problems_unsolved = max(0, total_problems - problems_solved)

        # Calculate learning streak (consecutive days with submissions)
        learning_streak = calculate_learning_streak(user)

        return {
            'learning_streak': learning_streak,
            'problems_solved': problems_solved,
            'problems_unsolved': problems_unsolved,
            'total_problems': total_problems
        }

    except Exception as e:
        error_logger.exception(f"Error getting user statistics for user {user.id}: {str(e)}")
        return {
            'learning_streak': 0,
            'problems_solved': 0,
            'problems_unsolved': 0,
            'total_problems': 0
        }

def calculate_learning_streak(user):
    """Calculate the user's current learning streak in days"""
    try:
        # Get all submission dates for the user, ordered by date descending
        submission_dates = db.session.query(
            func.date(Submission.timestamp)
        ).filter(
            Submission.user_id == user.id
        ).distinct().order_by(
            func.date(Submission.timestamp).desc()
        ).all()

        if not submission_dates:
            return 0

        # Convert to list of date objects
        dates = [date[0] for date in submission_dates]

        # Check if user has activity today or yesterday (to account for timezone differences)
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)

        # If no activity today or yesterday, streak is 0
        if dates[0] != today and dates[0] != yesterday:
            return 0

        # Count consecutive days
        streak = 1
        current_date = dates[0]

        for i in range(1, len(dates)):
            expected_date = current_date - timedelta(days=1)
            if dates[i] == expected_date:
                streak += 1
                current_date = dates[i]
            else:
                break

        return streak

    except Exception as e:
        error_logger.exception(f"Error calculating learning streak for user {user.id}: {str(e)}")
        return 0
