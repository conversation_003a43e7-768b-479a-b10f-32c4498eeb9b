{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-4xl">
        {% for module in content %}
        <!-- Module Card -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-8">
            <div class="p-6">
                <!-- Content Section -->
                {% if module.is_pdf %}
                    <div class="w-full" style="height: 800px;">
                        <iframe 
                            src="{{ url_for('serve_content_file', topic=module.pdf_path.split('/')[0], filename=module.pdf_path.split('/')[1]) }}"
                            class="w-full h-full rounded-lg border border-gray-200"
                            type="application/pdf">
                            <p>Your browser doesn't support embedded PDFs. You can 
                                <a href="{{ url_for('serve_content_file', topic=module.pdf_path.split('/')[0], filename=module.pdf_path.split('/')[1]) }}">
                                    download the PDF
                                </a> instead.
                            </p>
                        </iframe>
                    </div>
                {% else %}
                    <!-- Markdown Content -->
                    <div class="markdown-content">
                        {{ module.html | safe }}
                    </div>
                {% endif %}

                <!-- Practice Problems Section -->
                <div class="mt-8">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Practice Problems</h4>
                    <div class="overflow-hidden rounded-lg ring-1 ring-gray-900/5">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-indigo-50">
                                <tr>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 w-24">Status</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Problem</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 w-32">Difficulty</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                {% for problem in problems[module.name] %}
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                                        {% if problem.status is not none %}
                                            {% if problem.status == 2 %}
                                                <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                                                    <i class="fas fa-check-circle mr-1"></i>Correct
                                                </span>
                                            {% elif problem.status == 1 %}
                                                <span class="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-700 ring-1 ring-inset ring-yellow-600/20">
                                                    <i class="fas fa-exclamation-circle mr-1"></i>Partial
                                                </span>
                                            {% else %}
                                                <span class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">
                                                    <i class="fas fa-times-circle mr-1"></i>Incorrect
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                                                <i class="fas fa-circle mr-1"></i>Not Attempted
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm">
                                        <a href="{{ problem.source }}" target="_blank" 
                                           class="text-indigo-600 hover:text-indigo-900 font-medium">
                                            {{ problem.name }}
                                        </a>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm">
                                        <span class="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20">
                                            {{ problem.difficulty }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        {% if not loop.last %}
        <div class="relative my-8">
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
                <div class="w-full border-t border-gray-200"></div>
            </div>
            <div class="relative flex justify-center">
                <span class="bg-white px-2 text-sm text-gray-500">
                    <i class="fas fa-ellipsis-h"></i>
                </span>
            </div>
        </div>
        {% endif %}
        {% endfor %}
    </div>
</div>

<style>
    .markdown-content {
        line-height: 1.6;
        font-size: 1rem;
        color: #374151;
    }
    
    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3 {
        margin-top: 1.5em;
        margin-bottom: 0.75em;
        font-weight: 600;
        line-height: 1.25;
    }
    
    .markdown-content p {
        margin-bottom: 1em;
    }
    
    .markdown-content ul {
        list-style-type: disc;
        margin-bottom: 1em;
        padding-left: 1.5em;
    }
    
    .markdown-content ol {
        list-style-type: decimal;
        margin-bottom: 1em;
        padding-left: 1.5em;
    }
    
    .markdown-content ul ul {
        list-style-type: circle;
        margin-top: 0.5em;
    }
    
    .markdown-content ol ol {
        list-style-type: lower-alpha;
        margin-top: 0.5em;
    }
    
    .markdown-content li {
        margin-bottom: 0.5em;
        padding-left: 0.5em;
    }
    
    .markdown-content li::marker {
        color: #6b7280;
    }
    
    .markdown-content li > p {
        margin-top: 0.5em;
        margin-bottom: 0.5em;
    }
    
    .markdown-content li:last-child {
        margin-bottom: 0;
    }
    
    .markdown-content code {
        background-color: #f3f4f6;
        padding: 0.2em 0.4em;
        border-radius: 0.25rem;
        font-size: 0.875em;
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
    }
    
    .markdown-content pre {
        background-color: #f3f4f6;
        padding: 1em;
        border-radius: 0.5rem;
        overflow-x: auto;
        margin: 1em 0;
    }
    
    .markdown-content pre code {
        background-color: transparent;
        padding: 0;
    }
    
    .markdown-content blockquote {
        margin: 1em 0;
        padding-left: 1em;
        border-left: 4px solid #e5e7eb;
        color: #6b7280;
    }
    
    .markdown-content a {
        color: #4f46e5;
        text-decoration: none;
    }
    
    .markdown-content a:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}
