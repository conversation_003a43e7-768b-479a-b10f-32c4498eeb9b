{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8 min-h-screen bg-gradient-to-b from-white to-gray-50">
    <div class="mx-auto max-w-4xl">
        <!-- <PERSON>er with subtle animation -->
        <div class="text-center mb-12 fade-in-up">
            <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Your Submissions</h1>
            <p class="mt-3 text-lg text-gray-500 max-w-2xl mx-auto">Track your progress and review your past work</p>
        </div>

        <!-- Filters Card - Redesigned with better spacing and animations -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden mb-10 transform transition-all duration-300 hover:shadow-md">
            <div class="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">Filters</h2>
                <button id="toggle-filters" class="text-sm text-gray-500 hover:text-indigo-600 transition-colors duration-200 flex items-center gap-1">
                    <span id="toggle-text">Hide Filters</span>
                    <svg id="toggle-icon" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
            </div>
            <div id="filters-content" class="p-6 transition-all duration-300">
                <form method="GET" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Subject Filter - Enhanced styling -->
                        <div class="space-y-2">
                            <label for="subject_id" class="block text-sm font-medium text-gray-700">Subject</label>
                            <select name="subject_id" id="subject_id" class="block w-full rounded-md border-0 py-2.5 px-3.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm transition-all duration-200 hover:ring-gray-400">
                                <option value="">All Subjects</option>
                                {% for subject in subjects %}
                                    <option value="{{ subject.id }}" {% if request.args.get('subject_id') == subject.id|string %}selected{% endif %}>
                                        {{ subject.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Confidence Level Filter - Enhanced styling -->
                        <div class="space-y-2">
                            <label for="confidence_level" class="block text-sm font-medium text-gray-700">Difficulty Level</label>
                            <select name="confidence_level" id="confidence_level" class="block w-full rounded-md border-0 py-2.5 px-3.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm transition-all duration-200 hover:ring-gray-400">
                                <option value="">All Levels</option>
                                <option value="Easy" {% if request.args.get('confidence_level') == 'Easy' %}selected{% endif %}>Easy</option>
                                <option value="Medium" {% if request.args.get('confidence_level') == 'Medium' %}selected{% endif %}>Medium</option>
                                <option value="Difficult" {% if request.args.get('confidence_level') == 'Difficult' %}selected{% endif %}>Difficult</option>
                            </select>
                        </div>

                        <!-- Status Filter - Enhanced styling -->
                        <div class="space-y-2">
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" id="status" class="block w-full rounded-md border-0 py-2.5 px-3.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm transition-all duration-200 hover:ring-gray-400">
                                <option value="">All Statuses</option>
                                <option value="correct" {% if request.args.get('status') == 'correct' %}selected{% endif %}>Correct</option>
                                <option value="partial" {% if request.args.get('status') == 'partial' %}selected{% endif %}>Partially Correct</option>
                                <option value="incorrect" {% if request.args.get('status') == 'incorrect' %}selected{% endif %}>Incorrect</option>
                            </select>
                        </div>

                        <!-- Filter Buttons - Enhanced styling and layout -->
                        <div class="flex items-end gap-3">
                            <button type="submit" class="flex-1 rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-indigo-500 hover:shadow-md focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                <span class="flex items-center justify-center gap-1.5">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                                    </svg>
                                    Filter
                                </span>
                            </button>
                            <a href="{{ url_for('review') }}" class="flex-1 rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 transition-all duration-200 hover:bg-gray-50 hover:ring-gray-400 text-center">
                                <span class="flex items-center justify-center gap-1.5">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    Clear
                                </span>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Stats Summary - New component -->
        {% if submissions %}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Correct</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% set correct_count = namespace(value=0) %}
                            {% for submission in submissions %}
                                {% if submission.score is defined and submission.part.score is defined and submission.score is not none and submission.part.score is not none %}
                                    {% if submission.score|float == submission.part.score|float %}
                                        {% set correct_count.value = correct_count.value + 1 %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {{ correct_count.value }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Partial</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% set partial_count = namespace(value=0) %}
                            {% for submission in submissions %}
                                {% if submission.score is defined and submission.part.score is defined and submission.score is not none and submission.part.score is not none %}
                                    {% if submission.score|float > 0 and submission.score|float < submission.part.score|float %}
                                        {% set partial_count.value = partial_count.value + 1 %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {{ partial_count.value }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Incorrect</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% set incorrect_count = namespace(value=0) %}
                            {% for submission in submissions %}
                                {% if submission.score is defined and submission.part.score is defined and submission.score is not none and submission.part.score is not none %}
                                    {% if submission.score|float == 0 %}
                                        {% set incorrect_count.value = incorrect_count.value + 1 %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            {{ incorrect_count.value }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Submissions - Redesigned with cards instead of table for mobile-friendliness -->
        <div class="mb-10">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Your Submission History</h3>
            
            {% if submissions %}
            <div class="space-y-4">
                {% for submission in submissions %}
                <a href="{{ url_for('load_question', question_id=submission.question.id) }}" 
                   class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden block transition-all duration-300 hover:shadow-md transform hover:translate-y-[-2px]">
                    <div class="p-5">
                        <div class="flex items-start justify-between">
                            <div class="flex-1 min-w-0">
                                <h4 class="text-base font-medium text-gray-900 line-clamp-2 mb-2">{{ submission.question.description }}</h4>
                                <p class="text-sm text-gray-600 mb-3 line-clamp-1">{{ submission.part.description }}</p>
                                
                                <div class="flex items-center">
                                    <span class="flex h-2 w-2 rounded-full mr-2 
                                        {% if submission.score|float|int == submission.part.score|float|int %}bg-green-500
                                        {% elif submission.score is not none and submission.score|float > 0 %}bg-yellow-500
                                        {% else %}bg-red-500{% endif %}">
                                    </span>
                                    <span class="text-xs text-gray-600">
                                        {% if submission.score|float|int == submission.part.score|float|int %}Correct
                                        {% elif submission.score is not none and submission.score|float > 0 %}Partially Correct
                                        {% else %}Incorrect{% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4 flex-shrink-0">
                                <span class="inline-flex items-center rounded-md 
                                    {% if submission.score|float|int == submission.part.score|float|int %}bg-green-50 text-green-700 ring-green-600/20
                                    {% elif submission.score is not none and submission.score|float > 0 %}bg-yellow-50 text-yellow-700 ring-yellow-600/20
                                    {% else %}bg-red-50 text-red-700 ring-red-600/20{% endif %}
                                    px-2.5 py-1.5 text-sm font-medium ring-1 ring-inset">
                                    {{ submission.score|float|int }}/{{ submission.part.score|float|int }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="mt-4 flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {{ submission.timestamp.strftime('%b %d, %H:%M') }}
                            </div>
                            <span class="text-xs font-medium text-indigo-600 group-hover:text-indigo-500 inline-flex items-center">
                                View submission
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </span>
                        </div>
                    </div>
                </a>
                {% endfor %}
            </div>
            {% else %}
            <!-- Empty state - Redesigned with illustration -->
            <div class="text-center py-16 px-4 bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden">
                <svg class="mx-auto h-24 w-24 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No submissions yet</h3>
                <p class="mt-2 text-sm text-gray-500 max-w-md mx-auto">Start completing questions to track your progress and review your performance over time.</p>
                <div class="mt-6">
                    <a href="{{ url_for('index') }}" class="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-indigo-500 hover:shadow-md focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        Explore Questions
                    </a>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Progress Bar - New component -->
        {% if submissions %}
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden mb-10">
            <div class="p-6">
                <h3 class="text-sm font-medium text-gray-900 mb-2">Your Overall Progress</h3>
                <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                    {% set total_score = 0 %}
                    {% set max_score = 0 %}
                    {% for submission in submissions %}
                        {% if submission.score is defined and submission.part.score is defined and submission.score is not none and submission.part.score is not none %}
                            {% set total_score = total_score + submission.score|float %}
                            {% set max_score = max_score + submission.part.score|float %}
                        {% endif %}
                    {% endfor %}
                    {% set percentage = (total_score / max_score * 100) if max_score > 0 else 0 %}
                    <div class="bg-indigo-600 h-2.5 rounded-full" style="width: {{ percentage|int }}%"></div>
                </div>
                <div class="flex justify-between items-center text-xs text-gray-500">
                    <span>{{ total_score }} points earned</span>
                    <span>{{ percentage|int }}% complete</span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Navigation Footer - Enhanced with animation -->
        <div class="mt-10 text-center">
            <a href="{{ url_for('index') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                <svg class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Back to Home
            </a>
        </div>
    </div>
</div>

<style>
    /* Fade in up animation */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .fade-in-up {
        animation: fadeInUp 0.8s ease-out;
    }
    
    /* Hover scaling animation for cards */
    .card-zoom-hover {
        transition: transform 0.3s ease-in-out;
    }
    
    .card-zoom-hover:hover {
        transform: scale(1.02);
    }
    
    /* Hover effect for submission items */
    .submission-item {
        transition: all 0.3s ease;
    }
    
    .submission-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
</style>

<script>
    // Toggle filters visibility
    document.addEventListener('DOMContentLoaded', function() {
        const toggleButton = document.getElementById('toggle-filters');
        const filtersContent = document.getElementById('filters-content');
        const toggleIcon = document.getElementById('toggle-icon');
        const toggleText = document.getElementById('toggle-text');
        
        toggleButton.addEventListener('click', function() {
            if (filtersContent.style.maxHeight) {
                filtersContent.style.maxHeight = null;
                filtersContent.style.opacity = '0';
                filtersContent.style.transform = 'translateY(-10px)';
                toggleIcon.style.transform = 'rotate(0deg)';
                toggleText.textContent = 'Show Filters';
                
                // Hide after animation completes
                setTimeout(() => {
                    filtersContent.style.display = 'none';
                }, 300);
            } else {
                filtersContent.style.display = 'block';
                
                // Trigger reflow
                void filtersContent.offsetWidth;
                
                filtersContent.style.maxHeight = filtersContent.scrollHeight + 'px';
                filtersContent.style.opacity = '1';
                filtersContent.style.transform = 'translateY(0)';
                toggleIcon.style.transform = 'rotate(180deg)';
                toggleText.textContent = 'Hide Filters';
            }
        });
        
        // Staggered animation for cards
        const cards = document.querySelectorAll('.space-y-4 > a');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 + (index * 100)); // Staggered delay
        });
    });
</script>
{% endblock %}
