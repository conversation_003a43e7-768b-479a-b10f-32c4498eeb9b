{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 min-h-screen bg-gradient-to-b from-indigo-50 to-white">
    <div class="mx-auto max-w-3xl">
        <!-- Header with animation -->
        <div class="text-center mb-12 animate-fade-in-down">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 via-purple-600 to-violet-600">Activity Feed</h1>
            <p class="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">See what's happening in your groups</p>
        </div>

        <!-- Group selection if user is in multiple groups -->
        {% if user_groups|length > 1 %}
        <div class="mb-10 max-w-md mx-auto">
            <div class="relative">
                <label for="group-select" class="block text-sm font-medium text-gray-700 mb-2">Select Group</label>
                <select id="group-select" class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 pl-3 pr-10 appearance-none" onchange="window.location.href=`{{ url_for('feed') }}?group_id=${this.value}`">
                    {% for group in user_groups %}
                    <option value="{{ group.id }}" {% if selected_group and selected_group.id == group.id %}selected{% endif %}>{{ group.name }}</option>
                    {% endfor %}
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700" style="top: 24px;">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- No groups message -->
        {% if not user_groups %}
        <div class="text-center py-16 bg-white rounded-2xl shadow-sm border border-gray-100 max-w-lg mx-auto">
            <div class="mx-auto h-24 w-24 rounded-full bg-indigo-100 flex items-center justify-center mb-6 transform transition-all duration-500 hover:scale-110">
                <i class="fas fa-users text-indigo-600 text-3xl"></i>
            </div>
            <h3 class="text-2xl font-semibold text-gray-900">You're not in any groups yet</h3>
            <p class="mt-3 text-lg text-gray-500 max-w-md mx-auto">Join a group to see what others are working on and stay motivated together</p>
            <div class="mt-8">
                <a href="{{ url_for('groups') }}" class="inline-flex items-center rounded-lg bg-gradient-to-r from-indigo-600 to-violet-600 px-5 py-3 text-base font-semibold text-white shadow-md hover:from-indigo-500 hover:to-violet-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105">
                    <i class="fas fa-users-rectangle mr-2"></i>
                    Browse Groups
                </a>
            </div>
        </div>
        {% elif not activity_feed %}
        <div class="text-center py-16 bg-white rounded-2xl shadow-sm border border-gray-100 max-w-lg mx-auto">
            <div class="mx-auto h-24 w-24 rounded-full bg-indigo-100 flex items-center justify-center mb-6 transform transition-all duration-500 hover:scale-110">
                <i class="fas fa-bell-slash text-indigo-600 text-3xl"></i>
            </div>
            <h3 class="text-2xl font-semibold text-gray-900">No activity yet</h3>
            <p class="mt-3 text-lg text-gray-500 max-w-md mx-auto">There's no recent activity in this group. Check back later!</p>
        </div>
        {% else %}

        <!-- Activity Feed -->
        <div class="space-y-4">
            {% for activity in activity_feed %}
            <div class="bg-white shadow-md rounded-xl overflow-hidden border border-gray-100 transform transition-all duration-200 hover:shadow-lg hover:translate-y-[-2px]">
                <!-- Activity header with icon and timestamp -->
                <div class="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
                    <div class="flex items-center">
                        <!-- User avatar -->
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center shadow-sm">
                            <span class="text-white font-bold">{{ activity.user.username[:2].upper() }}</span>
                        </div>

                        <!-- Username -->
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900">{{ activity.user.username }}</p>
                        </div>
                    </div>

                    <!-- Timestamp -->
                    <span class="text-xs text-gray-500">
                        {{ activity.timestamp.strftime('%b %d, %H:%M') }}
                    </span>
                </div>

                <!-- Activity content based on type -->
                <div class="px-6 py-4">
                    {% if activity.type == 'goal_achieved' %}
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                            <i class="fas fa-trophy text-green-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-base font-medium text-gray-900">{{ activity.title }}</p>
                            <p class="mt-1 text-sm text-gray-500">
                                {{ activity.content }}
                            </p>

                            <!-- Progress bar -->
                            <div class="mt-3 w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-gradient-to-r from-green-500 to-green-600 h-2.5 rounded-full" style="width: {{ activity.data.percentage }}%"></div>
                            </div>
                        </div>
                    </div>

                    {% elif activity.type == 'streak_milestone' %}
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center">
                            <i class="fas fa-fire text-orange-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-base font-medium text-gray-900">{{ activity.title }}</p>
                            <p class="mt-1 text-sm text-gray-500">
                                {{ activity.content }}
                            </p>

                            <!-- Streak visualization -->
                            <div class="mt-3 flex space-x-1">
                                {% for i in range(min(activity.data.streak, 10)) %}
                                <div class="h-6 w-3 bg-gradient-to-t from-orange-500 to-orange-300 rounded-sm"></div>
                                {% endfor %}
                                {% if activity.data.streak > 10 %}
                                <div class="flex items-center ml-1">
                                    <span class="text-xs text-gray-500">+{{ activity.data.streak - 10 }} more</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% elif activity.type == 'first_activity' %}
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <i class="fas fa-play text-blue-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-base font-medium text-gray-900">{{ activity.title }}</p>
                            <p class="mt-1 text-sm text-gray-500">
                                {{ activity.content }}
                            </p>
                        </div>
                    </div>

                    {% elif activity.type == 'multiple_topics' %}
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                            <i class="fas fa-book-open text-purple-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-base font-medium text-gray-900">{{ activity.title }}</p>
                            <p class="mt-1 text-sm text-gray-500">
                                {{ activity.content }}
                            </p>

                            <!-- Topic tags -->
                            <div class="mt-3 flex flex-wrap gap-2">
                                {% for topic in activity.data.topics %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    {{ topic }}
                                </span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    {% elif activity.type == 'many_questions' %}
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <i class="fas fa-question text-indigo-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-base font-medium text-gray-900">{{ activity.title }}</p>
                            <p class="mt-1 text-sm text-gray-500">
                                {{ activity.content }}
                            </p>

                            <!-- Question count visualization -->
                            <div class="mt-3 flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    {% set width = activity.data.count * 5 %}
                                    {% if width > 100 %}
                                        {% set width = 100 %}
                                    {% endif %}
                                    <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 h-2.5 rounded-full" style="width: {{ width }}%"></div>
                                </div>
                                <span class="ml-2 text-xs font-medium text-gray-700">{{ activity.data.count }}</span>
                            </div>
                        </div>
                    </div>

                    {% elif activity.type == 'significant_time' %}
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-cyan-100 flex items-center justify-center">
                            <i class="fas fa-hourglass-half text-cyan-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-base font-medium text-gray-900">{{ activity.title }}</p>
                            <p class="mt-1 text-sm text-gray-500">
                                {{ activity.content }}
                            </p>

                            <!-- Time visualization -->
                            <div class="mt-3 flex items-center space-x-1">
                                {% for i in range(activity.data.hours) %}
                                <div class="h-8 w-4 bg-gradient-to-t from-cyan-500 to-cyan-300 rounded-sm"></div>
                                {% endfor %}
                                <div class="h-8 w-4 bg-gradient-to-t from-cyan-500 to-cyan-300 rounded-sm opacity-50"></div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Activity footer with interaction buttons -->
                <div class="px-6 py-3 bg-gray-50 flex justify-between">
                    <button class="text-sm text-gray-500 hover:text-indigo-600 flex items-center transition-colors duration-150">
                        <i class="fas fa-thumbs-up mr-1.5"></i> Encourage
                    </button>
                    <button class="text-sm text-gray-500 hover:text-indigo-600 flex items-center transition-colors duration-150">
                        <i class="fas fa-comment mr-1.5"></i> Comment
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
</div>

<style>
    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-down {
        animation: fadeInDown 0.6s ease-out;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Stagger the appearance of activity cards
        const activityCards = document.querySelectorAll('.bg-white.shadow-md.rounded-xl');
        activityCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('opacity-100');
                card.classList.remove('opacity-0', 'translate-y-4');
            }, 100 * index);
        });
    });
</script>
{% endblock %}
