{% extends "base.html" %}
{% block content %}
<body>
    <form id="answerForm" action="/testing">
        <math-field id="math-field"></math-field>
        <button type="submit">Submit</button>
    </form>

    <script>
        document.getElementById('answerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            const mathField = document.getElementById('math-field');

            const latex = mathField.getValue();
            const plainText = mathField.getValue('ascii-math'); // Convert to plain text
            console.log(latex, plainText);
            
            fetch('/testing', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    question_id: 123,
                    answer_latex: latex,   // For rendering
                    answer_text: plainText // For grading with sympy
                }),
            });
        });
    </script>
</body>
{% endblock %}