{% extends "base.html" %}

{% block content %}
<div class="w-full mx-auto px-6 sm:px-8 lg:px-10 py-8">
    <div class="md:grid md:grid-cols-6 md:gap-8">
        <div class="md:col-span-1">
            <div class="px-4 sm:px-0 sticky top-24">
                <h3 class="text-xl font-medium leading-6 text-gray-900">Edit Problem Set</h3>
                <p class="mt-3 text-sm text-gray-600">
                    Modify the problem set by updating questions and group sharing settings.
                </p>
                <div class="mt-6 border-t border-gray-200 pt-4">
                    <h4 class="text-sm font-medium text-gray-500">Tips</h4>
                    <ul class="mt-2 text-sm text-gray-600 space-y-2">
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Search for questions by typing in the search box
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Hover over questions to see details
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="mt-5 md:col-span-5 md:mt-0">
            <form action="{{ url_for('edit_problemset', id=problemset.id) }}" method="POST" enctype="multipart/form-data">
                <div class="shadow sm:overflow-hidden sm:rounded-md">
                    <div class="space-y-6 bg-white px-4 py-5 sm:p-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                            <div class="mt-1">
                                <input type="text" name="name" id="name" required
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    placeholder="Enter problem set name"
                                    value="{{ problemset.name }}">
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <div class="mt-1">
                                <textarea name="description" id="description" rows="3"
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    placeholder="Enter problem set description">{{ problemset.description or '' }}</textarea>
                            </div>
                        </div>

                        <!-- PDF Attachment -->
                        <div>
                            <label for="pdf_attachment" class="block text-sm font-medium text-gray-700">PDF Attachment (Original Questions Document)</label>
                            <div class="mt-1">
                                <input type="file" name="pdf_attachment" id="pdf_attachment" accept=".pdf"
                                    class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                            </div>

                            {% if problemset.pdf_attachment %}
                            <div class="mt-2 flex items-center">
                                <div class="flex-1">
                                    <p class="text-sm text-gray-700">Current attachment: <a href="{{ url_for('serve.serve_file', filename=problemset.pdf_attachment) }}" target="_blank" class="text-indigo-600 hover:text-indigo-800">{{ problemset.pdf_attachment }}</a></p>
                                </div>
                                <div class="ml-4 flex items-center">
                                    <input id="remove_pdf" name="remove_pdf" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                    <label for="remove_pdf" class="ml-2 block text-sm text-gray-700">Remove PDF</label>
                                </div>
                            </div>
                            {% else %}
                            <p class="mt-1 text-sm text-gray-500">Attach the original PDF document containing the questions</p>
                            {% endif %}
                        </div>

                        <!-- Questions -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Questions</label>

                            <!-- Search input -->
                            <div class="mt-2 mb-3 relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <input type="text" id="question-search" placeholder="Search questions..."
                                    class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    oninput="filterQuestions(this.value.trim())">
                            </div>

                            <!-- Questions list -->
                            <div id="questions-container" class="mt-1 max-h-96 overflow-y-auto p-2 border border-gray-200 rounded-md bg-gray-50">
                                {% for question in questions %}
                                <div class="relative flex items-start py-2 question-item"
                                     data-question-id="{{ question.id }}"
                                     data-question-text="{{ question.title or '' }}{{ ' - ' + question.description if question.description else '' }}{% if question.parts and question.parts|length > 0 %} {{ question.parts[0].description }}{% endif %}">
                                    <div class="min-w-0 flex-1 text-sm">
                                        <div class="flex items-center">
                                            <label for="question-{{ question.id }}" class="select-none font-medium text-gray-700 hover:text-indigo-600 cursor-pointer mr-2"
                                                   title="{% if question.parts and question.parts|length > 0 %}{{ question.parts[0].description|striptags }}{% endif %}">
                                                {% if question.title %}
                                                    {{ question.title }}
                                                {% else %}
                                                    No Title
                                                {% endif %}
                                            </label>
                                            <a href="{{ url_for('load_question', question_id=question.id) }}" target="_blank" class="text-indigo-600 hover:text-indigo-800" title="View question details">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="ml-3 flex h-5 items-center">
                                        <input type="checkbox" name="questions" value="{{ question.id }}"
                                            id="question-{{ question.id }}"
                                            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 question-checkbox"
                                            {% if question in problemset.questions %}checked{% endif %}>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>

                            <!-- No results message -->
                            <div id="no-questions-found" class="hidden py-3 text-center text-sm text-gray-500">
                                No questions found matching your search.
                            </div>
                        </div>

                        <!-- Groups -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Share with Groups</label>
                            <div class="mt-1 max-h-60 overflow-y-auto p-2 border border-gray-200 rounded-md bg-gray-50">
                                {% for group in groups %}
                                <div class="relative flex items-start py-2">
                                    <div class="min-w-0 flex-1 text-sm">
                                        <label for="group-{{ group.id }}" class="select-none font-medium text-gray-700">
                                            {{ group.name }}
                                        </label>
                                        {% if group.description %}
                                        <p class="text-gray-500">{{ group.description }}</p>
                                        {% endif %}
                                    </div>
                                    <div class="ml-3 flex h-5 items-center">
                                        <input type="checkbox" name="groups" value="{{ group.id }}"
                                            id="group-{{ group.id }}"
                                            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                            {% if group in problemset.shared_with %}checked{% endif %}>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}"
                            class="inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                            Cancel
                        </a>
                        <button type="submit"
                            class="ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                            Save Changes
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
    <script>
        // Client-side filtering function - defined globally for the inline handler
        function filterQuestions(searchTerm) {
            searchTerm = searchTerm.toLowerCase();
            let visibleCount = 0;
            const questionItems = document.querySelectorAll('.question-item');
            const noQuestionsFound = document.getElementById('no-questions-found');

            questionItems.forEach(item => {
                const questionText = item.getAttribute('data-question-text').toLowerCase();

                if (questionText.includes(searchTerm)) {
                    item.classList.remove('hidden');
                    visibleCount++;
                } else {
                    item.classList.add('hidden');
                }
            });

            // Show/hide no results message
            if (visibleCount === 0 && noQuestionsFound) {
                noQuestionsFound.classList.remove('hidden');
            } else if (noQuestionsFound) {
                noQuestionsFound.classList.add('hidden');
            }
        }
    </script>
{% endblock %}