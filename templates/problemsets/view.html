{% extends "base.html" %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="lg:flex lg:items-center lg:justify-between">
        <div class="min-w-0 flex-1">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                {{ problemset.name }}
            </h2>
            {% if problemset.description %}
            <p class="mt-2 text-lg text-gray-500">
                {{ problemset.description }}
            </p>
            {% endif %}
            <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
                <div class="mt-2 flex items-center text-sm text-gray-500">
                    <i class="fas fa-user-circle flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"></i>
                    Created by {{ problemset.creator.username }}
                </div>
                <div class="mt-2 flex items-center text-sm text-gray-500">
                    <i class="fas fa-calendar flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"></i>
                    Created {{ problemset.created_at.strftime('%B %d, %Y') }}
                </div>
                <div class="mt-2 flex items-center text-sm text-gray-500">
                    <i class="fas fa-book flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"></i>
                    {{ problemset.questions|length }} questions
                </div>
                {% if problemset.shared_with %}
                <div class="mt-2 flex items-center text-sm text-gray-500">
                    <i class="fas fa-users flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"></i>
                    Shared with {{ problemset.shared_with|length }} group{{ 's' if problemset.shared_with|length != 1 }}
                </div>
                {% endif %}
                {% if problemset.pdf_attachment %}
                <div class="mt-2 flex items-center text-sm text-gray-500">
                    <i class="fas fa-file-pdf flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"></i>
                    <a href="{{ url_for('serve.serve_file', filename=problemset.pdf_attachment) }}" target="_blank" class="text-indigo-600 hover:text-indigo-800">View Original PDF</a>
                </div>
                {% endif %}
            </div>
        </div>
        <div class="mt-5 flex lg:ml-4 lg:mt-0">
            <span class="sm:ml-3">
                <a href="{{ url_for('do_problemset', id=problemset.id) }}"
                    class="inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600">
                    <i class="fas fa-play -ml-0.5 mr-1.5 h-5 w-5"></i>
                    Start
                </a>
            </span>
            {% if problemset.created_by == session.user_id %}
            <span class="sm:ml-3">
                <a href="{{ url_for('edit_problemset', id=problemset.id) }}"
                    class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                    <i class="fas fa-edit -ml-0.5 mr-1.5 h-5 w-5"></i>
                    Edit
                </a>
            </span>
            <span class="sm:ml-3">
                <form action="{{ url_for('delete_problemset', id=problemset.id) }}" method="POST" class="inline"
                    onsubmit="return confirm('Are you sure you want to delete this problem set?');">
                    <button type="submit"
                        class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
                        <i class="fas fa-trash -ml-0.5 mr-1.5 h-5 w-5"></i>
                        Delete
                    </button>
                </form>
            </span>
            {% endif %}
        </div>
    </div>

    <!-- Questions List -->
    <div class="mt-8">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h3 class="text-base font-semibold leading-6 text-gray-900">Questions</h3>
                <p class="mt-2 text-sm text-gray-700">A list of all questions in this problem set.</p>
            </div>
        </div>
        <div class="mt-8 flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                                        Question</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Topic</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Subject</th>
                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                        <span class="sr-only">View</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                {% for question in problemset.questions %}
                                <tr>
                                    <td class="whitespace-normal py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                        {{ question.description }}
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {% if question.topic %}
                                            {{ question.topic.name }}
                                        {% else %}
                                            <span class="text-gray-400">No topic</span>
                                        {% endif %}
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {% if question.topic %}
                                            {{ question.topic.subject.name }}
                                        {% else %}
                                            <span class="text-gray-400">{{ subject_name }}</span>
                                        {% endif %}
                                    </td>
                                    <td
                                        class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                        <a href="{{ url_for('load_question', question_id=question.id) }}" class="text-indigo-600 hover:text-indigo-900">
                                            View<span class="sr-only">, {{ question.description }}</span>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submissions List -->
    <div class="mt-8">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h3 class="text-base font-semibold leading-6 text-gray-900">Submissions</h3>
                <p class="mt-2 text-sm text-gray-700">A list of all submissions for this problem set.</p>
            </div>
        </div>
        <div class="mt-8 flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                        {% if problemset.submissions %}
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">User</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Submitted</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Score</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status</th>
                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                        <span class="sr-only">View</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                {% for submission in problemset.submissions|sort(attribute='submitted_at', reverse=true) %}
                                <tr>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                        <div class="flex items-center">
                                            <i class="fas fa-user-circle text-gray-400 mr-2"></i>
                                            {{ submission.user.username }}
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {{ submission.submitted_at.strftime('%Y-%m-%d %H:%M:%S') }}
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        {{ "%.1f"|format(submission.total_score) }}/{{ "%.1f"|format(submission.max_score) }}
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm">
                                        <span class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium
                                            {% if submission.status == 'completed' %}
                                                bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20
                                            {% elif submission.status == 'in_progress' %}
                                                bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20
                                            {% else %}
                                                bg-gray-50 text-gray-700 ring-1 ring-inset ring-gray-600/20
                                            {% endif %}">
                                            {{ submission.status|title }}
                                        </span>
                                    </td>
                                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                        <a href="{{ url_for('problemset_submission_details', submission_id=submission.id) }}" class="text-indigo-600 hover:text-indigo-900">
                                            View<span class="sr-only">, submission by {{ submission.user.username }}</span>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        {% else %}
                        <div class="text-center py-12">
                            <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">No submissions yet</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Shared Groups -->
    {% if problemset.shared_with %}
    <div class="mt-8">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h3 class="text-base font-semibold leading-6 text-gray-900">Shared With</h3>
                <p class="mt-2 text-sm text-gray-700">Groups that have access to this problem set.</p>
            </div>
        </div>
        <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {% for group in problemset.shared_with %}
            <div class="relative flex items-center space-x-3 rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:border-gray-400">
                <div class="flex-shrink-0">
                    <i class="fas fa-users text-2xl text-gray-400"></i>
                </div>
                <div class="min-w-0 flex-1">
                    <a href="#" class="focus:outline-none">
                        <span class="absolute inset-0" aria-hidden="true"></span>
                        <p class="text-sm font-medium text-gray-900">{{ group.name }}</p>
                        {% if group.description %}
                        <p class="truncate text-sm text-gray-500">{{ group.description }}</p>
                        {% endif %}
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}