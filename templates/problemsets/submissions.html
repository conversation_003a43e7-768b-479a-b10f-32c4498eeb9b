{% extends "base.html" %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Submissions for {{ problemset.name }}</h1>
            <p class="mt-1 text-sm text-gray-500">View all submissions for this problem set</p>
        </div>
        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <i class="fas fa-arrow-left mr-2"></i>Back to Problem Set
        </a>
    </div>

    {% if submissions %}
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6 bg-gray-50">
                <h3 class="text-lg leading-6 font-medium text-gray-900">All Submissions</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ submissions|length }} submission(s) found</p>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for submission in submissions %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-indigo-100 text-indigo-500">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ submission.user.username }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ submission.submitted_at.strftime('%Y-%m-%d') }}</div>
                                    <div class="text-sm text-gray-500">{{ submission.submitted_at.strftime('%H:%M:%S') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if submission.status == 'completed' %}
                                            bg-green-100 text-green-800
                                        {% elif submission.status == 'in_progress' %}
                                            bg-yellow-100 text-yellow-800
                                        {% else %}
                                            bg-gray-100 text-gray-800
                                        {% endif %}">
                                        {{ submission.status|title }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium
                                            {% if submission.total_score == submission.max_score %}
                                                bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20
                                            {% elif submission.total_score > 0 %}
                                                bg-yellow-50 text-yellow-700 ring-1 ring-inset ring-yellow-600/20
                                            {% else %}
                                                bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20
                                            {% endif %}">
                                            {{ submission.total_score }}/{{ submission.max_score }}
                                        </span>
                                        {% if submission.max_score > 0 %}
                                        <span class="ml-2 text-sm text-gray-500">
                                            ({{ "%.1f"|format(submission.total_score / submission.max_score * 100) }}%)
                                        </span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{{ url_for('problemset_submission_details', submission_id=submission.id) }}" class="text-indigo-600 hover:text-indigo-900 inline-flex items-center">
                                        View Details
                                        <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6 bg-gray-50">
                <h3 class="text-lg leading-6 font-medium text-gray-900">No Submissions Found</h3>
            </div>
            <div class="text-center py-12">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100">
                    <i class="fas fa-history text-gray-400 text-2xl"></i>
                </div>
                <h3 class="mt-3 text-sm font-medium text-gray-900">No submissions yet</h3>
                <p class="mt-1 text-sm text-gray-500">There are no submissions for this problem set yet.</p>
                <div class="mt-6">
                    <a href="{{ url_for('do_problemset', id=problemset.id) }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-edit mr-2"></i>
                        Start Working
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}