{% extends "base.html" %}

{% block content %}
<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-3xl font-bold tracking-tight text-gray-900">Set Your New Password</h1>
            <p class="mt-2 text-sm text-gray-600">
                Enter the OTP sent to your email, then set and confirm your new password.
            </p>
        </div>

        <!-- Reset Password Form -->
        <div class="mt-8 bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
            {# Display flashed messages if any #}
            {% with messages = get_flashed_messages(with_categories=true) %}
              {% if messages %}
                <div class="mb-4">
                  {% for category, message in messages %}
                    <div class="p-3 rounded text-center text-sm
                                {% if category == 'error' %} bg-red-100 text-red-700
                                {% elif category == 'success' %} bg-green-100 text-green-700
                                {% elif category == 'warning' %} bg-yellow-100 text-yellow-700
                                {% else %} bg-blue-100 text-blue-700 {% endif %}" role="alert">
                      {{ message }}
                    </div>
                  {% endfor %}
                </div>
              {% endif %}
            {% endwith %}

            {# Use standard Flask form submission #}
            <form method="POST" action="{{ url_for('reset_password') }}" class="space-y-6">
                {# Hidden field for CSRF token if using Flask-WTF (recommended) #}
                {# {{ form.csrf_token }} #}

                {# Email Field (Read-only if pre-filled) #}
                <div>
                    <label for="email" class="block text-sm font-medium leading-6 text-gray-900">
                        Email Address
                    </label>
                    <div class="mt-2">
                        <input type="email" id="email" name="email" required readonly
                               value="{{ email or '' }}" {# Pre-fill from Flask context #}
                               class="block w-full rounded-md border-0 py-1.5 text-gray-900 bg-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                    </div>
                </div>

                {# OTP Field #}
                <div>
                    <label for="otp" class="block text-sm font-medium leading-6 text-gray-900">
                        One-Time Password (OTP)
                    </label>
                    <div class="mt-2">
                        <input type="text" id="otp" name="otp" required inputmode="numeric" pattern="\d{6}" maxlength="6"
                               placeholder="Enter 6-digit OTP"
                               class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                    </div>
                </div>

                {# New Password Field #}
                <div>
                    <label for="new_password" class="block text-sm font-medium leading-6 text-gray-900">
                        New Password
                    </label>
                    <div class="mt-2">
                        <input type="password" id="new_password" name="new_password" required
                               class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                    </div>
                </div>

                {# Confirm Password Field #}
                <div>
                    <label for="confirm_password" class="block text-sm font-medium leading-6 text-gray-900">
                        Confirm New Password
                    </label>
                    <div class="mt-2">
                        <input type="password" id="confirm_password" name="confirm_password" required
                               class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                    </div>
                </div>

                {# Submit Button #}
                <div>
                    <button type="submit" class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        Reset Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{# Remove the old script block and Supabase CDN link #}
{# No client-side JS needed for basic form submission #}

{% endblock %}
