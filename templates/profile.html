{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Profile Header with animated gradient -->
    <div class="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl overflow-hidden mb-8 shadow-lg transform transition-all duration-300 hover:shadow-xl">
        <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
        <div class="relative p-8">
            <div class="flex flex-col md:flex-row items-center md:items-start gap-6">
                <!-- Avatar -->
                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white text-3xl font-bold shadow-lg transform transition-all duration-300 hover:scale-105">
                    {{ profile_owner.username[:1].upper() }}
                </div>

                <!-- User Info -->
                <div class="flex-1 text-center md:text-left">
                    <h1 class="text-3xl font-bold text-white tracking-tight">{{ profile_owner.username }}</h1>
                    <div class="mt-2 flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
                        <span class="inline-flex items-center rounded-full bg-white/20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm">
                            <i class="fas fa-envelope mr-2"></i>
                            {{ profile_owner.email }}
                        </span>
                        <span class="inline-flex items-center rounded-full bg-white/20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm">
                            <i class="fas fa-clock mr-2"></i>
                            <span id="daily-active-timer">{{ daily_active_time | format_time }}</span> today
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-8 space-y-6">
            <!-- Activity Heatmap -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Time Spent Heatmap</h2>
                            <p class="text-sm text-gray-500">Your daily learning time</p>
                        </div>
                    </div>

                    <div class="relative">
                        <div class="heatmap" id="activityHeatmap"></div>
                        <div id="ctooltip" class="fixed bg-gray-900 text-white text-xs rounded px-2 py-1 z-50 pointer-events-none"></div>

                        <!-- Heatmap Legend -->
                        <div class="flex items-center justify-end mt-4 text-xs text-gray-600">
                            <span class="mr-1">Less</span>
                            <div class="flex space-x-1">
                                <div class="w-3 h-3 bg-gray-200 rounded-sm"></div>
                                <div class="w-3 h-3 bg-green-200 rounded-sm" title="Up to 30 minutes"></div>
                                <div class="w-3 h-3 bg-green-300 rounded-sm" title="30 minutes to 1 hour"></div>
                                <div class="w-3 h-3 bg-green-400 rounded-sm" title="1 to 2 hours"></div>
                                <div class="w-3 h-3 bg-green-500 rounded-sm" title="More than 2 hours"></div>
                            </div>
                            <span class="ml-1">More</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Submissions -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-history"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Recent Submissions</h2>
                            <p class="text-sm text-gray-500">Your latest answers</p>
                        </div>
                    </div>

                    {% if submissions %}
                    <div class="grid gap-4">
                        {% for submission in submissions %}
                        <a href="{{ url_for('submission_details', submission_id=submission.id) }}"
                           class="block bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md hover:bg-white transform hover:-translate-y-1 border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        {{ submission.part.question.description }}
                                    </p>
                                    <div class="flex items-center mt-1">
                                        <span class="text-xs text-gray-500 flex items-center">
                                            <i class="fas fa-puzzle-piece mr-1 text-indigo-400"></i>
                                            Part {{ submission.part.id }}
                                        </span>
                                        <span class="mx-2 text-gray-300">•</span>
                                        <span class="text-xs text-gray-500 flex items-center">
                                            <i class="fas fa-clock mr-1 text-indigo-400"></i>
                                            {{ submission.timestamp.strftime('%b %d, %Y at %H:%M') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4 flex-shrink-0">
                                    <div class="relative w-12 h-12">
                                        <svg class="w-full h-full" viewBox="0 0 36 36">
                                            <path class="stroke-current text-gray-200" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                            <path class="stroke-current {% if submission.score == submission.part.score %}text-green-500{% elif submission.score > 0 %}text-amber-500{% else %}text-red-500{% endif %}" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="{{ (submission.part.score > 0) and ((submission.score / submission.part.score) * 100) or 0 }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                            <text x="18" y="20.5" class="fill-current {% if submission.score == submission.part.score %}text-green-700{% elif submission.score > 0 %}text-amber-700{% else %}text-red-700{% endif %} font-bold text-xs" text-anchor="middle">{{ (submission.part.score > 0) and ((submission.score / submission.part.score) * 100) | int or 0 }}%</text>
                                        </svg>
                                    </div>
                                    <div class="text-xs font-medium text-center {% if submission.score == submission.part.score %}text-green-600{% elif submission.score > 0 %}text-amber-600{% else %}text-red-600{% endif %} mt-1">
                                        {{ submission.score }}/{{ submission.part.score }}
                                    </div>
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="flex flex-col items-center justify-center py-12 bg-gray-50 rounded-lg">
                        <div class="w-16 h-16 rounded-full bg-indigo-50 flex items-center justify-center mb-4">
                            <i class="fas fa-clipboard-list text-indigo-300 text-xl"></i>
                        </div>
                        <h3 class="text-base font-medium text-gray-900 mb-1">No submissions yet</h3>
                        <p class="text-gray-500 text-center max-w-md text-sm">
                            Start solving problems to see your submissions here
                        </p>
                        <a href="{{ url_for('vault') }}"
                           class="mt-4 inline-flex items-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-indigo-500 transition-colors duration-200">
                            <i class="fas fa-book-open mr-2"></i>
                            Go to Question Vault
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-4 space-y-6">
            <!-- Progress Stats -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Progress</h2>
                    </div>

                    <div class="flex flex-col items-center">
                        <!-- Removed Doughnut Chart -->
                        <!-- Display Correct Count centrally -->
                         <div class="relative w-40 h-40 mb-4 flex items-center justify-center flex-col bg-green-50 rounded-full border-4 border-green-100">
                            <span class="text-4xl font-bold text-green-600">{{ correct_count }}</span>
                            <span class="text-sm text-green-500 mt-1">Correct</span>
                        </div>

                        <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 w-full mt-4">
                            <div class="flex flex-col items-center p-3 bg-green-50 rounded-lg text-center">
                                <span class="text-lg font-semibold text-green-600">{{ correct_count }}</span>
                                <span class="text-xs text-gray-500">Correct</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-amber-50 rounded-lg text-center">
                                <span class="text-lg font-semibold text-amber-600">{{ partial_count }}</span>
                                <span class="text-xs text-gray-500">Partial</span>
                            </div>
                             <div class="flex flex-col items-center p-3 bg-red-50 rounded-lg text-center">
                                <span class="text-lg font-semibold text-red-600">{{ incorrect_count }}</span>
                                <span class="text-xs text-gray-500">Incorrect</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg text-center">
                                <span class="text-lg font-semibold text-gray-600">{{ unattempted_count }}</span>
                                <span class="text-xs text-gray-500">Unattempted</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Tracking -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-hourglass-half"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Time Tracking</h2>
                    </div>

                    <div class="flex flex-col items-center p-6 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg">
                        <div class="text-4xl font-bold text-indigo-600 mb-2" id="total-time-count">Loading...</div>
                        <div class="text-sm text-gray-600 mb-4">total time spent</div>

                        <!-- Progress Ring -->
                        <div class="relative w-48 h-48 mb-4">
                            <svg class="w-full h-full" viewBox="0 0 100 100">
                                <!-- Background circle -->
                                <circle class="text-gray-200" stroke-width="8" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" />
                                <!-- Progress circle -->
                                <circle class="text-indigo-600 transition-all duration-1000 ease-in-out" id="progress-ring"
                                    stroke-width="8"
                                    stroke="currentColor"
                                    fill="transparent"
                                    r="45"
                                    cx="50"
                                    cy="50"
                                    stroke-dasharray="0 283"
                                    stroke-linecap="round"
                                    transform="rotate(-90 50 50)" />
                            </svg>
                            <div class="absolute inset-0 flex flex-col items-center justify-center">
                                <div class="text-2xl font-bold text-indigo-600" id="today-time-count">Loading...</div>
                                <div class="text-sm text-gray-600">of <span id="daily-goal-display">60 min</span></div>
                                <div class="text-sm font-medium text-indigo-600 mt-1" id="progress-percent">0%</div>
                            </div>
                        </div>

                        <!-- Time Stats -->
                        <div class="grid grid-cols-2 gap-4 w-full mt-2">
                            <div class="flex flex-col items-center p-3 bg-white/50 rounded-lg shadow-sm">
                                <div class="text-2xl font-semibold text-indigo-600" id="avg-time-count">Loading...</div>
                                <div class="text-xs text-gray-500">Daily Average</div>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-white/50 rounded-lg shadow-sm">
                                <div class="text-2xl font-semibold text-indigo-600" id="streak-days">0</div>
                                <div class="text-xs text-gray-500">Day Streak</div>
                            </div>
                        </div>

                        <!-- Goal Setting (only visible to profile owner) -->
                        {% if session.user_id == profile_owner.id %}
                        <div class="w-full mt-4 pt-4 border-t border-indigo-100">
                            <div class="text-sm font-medium text-gray-700 mb-2">Set Daily Study Goal</div>
                            <div class="flex items-center">
                                <input type="number" id="goal-minutes" min="1" max="1440" class="w-20 px-2 py-1 border border-gray-300 rounded-md text-center mr-2" placeholder="60">
                                <span class="text-gray-600 mr-4">minutes</span>
                                <button id="save-goal-btn" class="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors text-sm">
                                    Save Goal
                                </button>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Learning Streak -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-fire"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Learning Streak</h2>
                    </div>

                    <div class="flex flex-col items-center p-6 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg">
                        <div class="text-4xl font-bold text-indigo-600 mb-2" id="streak-count">7</div>
                        <div class="text-sm text-gray-600 mb-4">days in a row</div>

                        <div class="flex space-x-1 mt-2">
                            <div class="w-8 h-2 rounded-full bg-indigo-600"></div>
                            <div class="w-8 h-2 rounded-full bg-indigo-600"></div>
                            <div class="w-8 h-2 rounded-full bg-indigo-600"></div>
                            <div class="w-8 h-2 rounded-full bg-indigo-600"></div>
                            <div class="w-8 h-2 rounded-full bg-indigo-600"></div>
                            <div class="w-8 h-2 rounded-full bg-indigo-600"></div>
                            <div class="w-8 h-2 rounded-full bg-indigo-600"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Management -->
            {% if session.user_id == profile_owner.id %}
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-red-100 text-red-600 mr-4">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Account Management</h2>
                    </div>

                    <div class="flex flex-col items-center">
                        <button id="delete-account-btn" class="mt-2 inline-flex items-center rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 transition-colors duration-200">
                            <i class="fas fa-user-times mr-2"></i>
                            Delete Account
                        </button>
                        <p class="text-xs text-gray-500 mt-2 text-center">This action cannot be undone. All your data will be permanently deleted.</p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
    /* Animated background grid */
    .bg-grid-white {
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    }

    /* Subtle animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Circular progress animation */
    @keyframes progress {
        0% {
            stroke-dasharray: 0 100;
        }
    }

    .stroke-current {
        animation: progress 1s ease-out forwards;
    }

    /* Heatmap styling */
    .heatmap {
        display: grid;
        grid-template-columns: repeat(53, 1fr);
        gap: 2px;
        padding: 1px;
    }

    .heatmap-cell {
        aspect-ratio: 1;
        width: 100%;
        background-color: #ebedf0;
        border-radius: 2px;
        transition: all 0.2s ease;
    }

    .heatmap-cell:hover {
        transform: scale(1.2);
    }

    .heatmap-cell[data-level="1"] { background-color: #bbf7d0; } /* green-200 */
    .heatmap-cell[data-level="2"] { background-color: #86efac; } /* green-300 */
    .heatmap-cell[data-level="3"] { background-color: #4ade80; } /* green-400 */
    .heatmap-cell[data-level="4"] { background-color: #22c55e; } /* green-500 */

#ctooltip {
    display: none;
    position: absolute; /* Changed from fixed */
    /* transform: translate(-50%, -100%); Removed transform */
    margin-top: -8px; /* Keep margin for spacing */
    backdrop-filter: blur(8px);
    background-color: rgba(0, 0, 0, 0.8);
        border-radius: 4px;
        padding: 6px 10px;
        font-weight: 500;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add staggered animation to cards
        const cards = document.querySelectorAll('.rounded-xl');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(10px)';
            card.style.transition = 'all 0.3s ease-out';
            card.style.transitionDelay = `${index * 0.1}s`;

            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });

        // Progress Chart - Removed Initialization

        // Activity Heatmap
        const heatmap = document.getElementById('activityHeatmap');
        const tooltip = document.getElementById('ctooltip');

        // Add a timestamp to prevent caching
        const cacheBuster = new Date().getTime();
        fetch(`/get_daily_time_data/{{ profile_owner.id }}?_=${cacheBuster}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("Daily time data received:", data);

                const today = new Date();
                const startDate = new Date(today);
                startDate.setDate(today.getDate() - 364); // 53 weeks

                let currentDate = new Date(startDate);
                while (currentDate <= today) {
                    const cell = document.createElement('div');
                    cell.className = 'heatmap-cell';

                    const dateStr = currentDate.toISOString().split('T')[0];
                    const timeInSeconds = data[dateStr] || 0;

                    // Define time thresholds for heatmap levels (in seconds)
                    // Level 1: > 0 minutes
                    // Level 2: > 30 minutes
                    // Level 3: > 1 hour
                    // Level 4: > 2 hours
                    let level = 0;
                    if (timeInSeconds > 0) level = 1;
                    if (timeInSeconds > 1800) level = 2; // 30 minutes
                    if (timeInSeconds > 3600) level = 3; // 1 hour
                    if (timeInSeconds > 7200) level = 4; // 2 hours

                    cell.setAttribute('data-level', level);

                    // Format date for tooltip
                    const formattedDate = new Date(dateStr).toLocaleDateString('en-US', {
                        weekday: 'short',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });

                    cell.addEventListener('mouseover', function(event) {
                        // Format time for display
                        const timeDisplay = formatTime(timeInSeconds);
                        tooltip.innerHTML = `<div class="font-medium">${formattedDate}</div><div>${timeDisplay} active time</div>`;
                        tooltip.style.display = 'block';

                        // Position relative to the cell within its container
                        tooltip.style.left = `${cell.offsetLeft + cell.offsetWidth / 2}px`;
                        tooltip.style.top = `${cell.offsetTop}px`;
                        tooltip.style.transform = 'translate(-50%, -110%)'; // Center horizontally and position above the cell
                    });

                    cell.addEventListener('mouseout', function() {
                        tooltip.style.transform = ''; // Reset transform on mouseout
                        tooltip.style.display = 'none';
                    });

                    heatmap.appendChild(cell);
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            });

        // Format time for display (reusable function)
        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        }

        // Update progress ring
        function updateProgressRing(percent) {
            const progressRing = document.getElementById('progress-ring');
            const circumference = 2 * Math.PI * 45; // 2πr where r=45
            const dashArray = (percent / 100) * circumference;

            // Update the stroke-dasharray property
            progressRing.style.strokeDasharray = `${dashArray} ${circumference}`;

            // Update the progress percentage text
            document.getElementById('progress-percent').textContent = `${percent}%`;

            // Update color based on progress
            if (percent >= 100) {
                progressRing.classList.remove('text-indigo-600');
                progressRing.classList.add('text-green-500');
            } else {
                progressRing.classList.remove('text-green-500');
                progressRing.classList.add('text-indigo-600');
            }
        }

        // Fetch time tracking data
        function fetchTimeData() {
            fetch(`/get_active_time`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Time tracking data received:", data);

                    if (data.status === 'success') {
                        // Update time tracking displays
                        document.getElementById('total-time-count').textContent = formatTime(data.total_time);
                        document.getElementById('today-time-count').textContent = formatTime(data.today_time);

                        // Update goal display
                        const goalMinutes = Math.floor(data.daily_goal / 60);
                        document.getElementById('daily-goal-display').textContent = `${goalMinutes} min`;

                        // Set the input value if the goal input exists
                        const goalInput = document.getElementById('goal-minutes');
                        if (goalInput) {
                            goalInput.value = goalMinutes;
                        }

                        // Update progress ring
                        updateProgressRing(data.progress_percent);

                        // Calculate average time (if we have time data)
                        fetch(`/get_daily_time_data/{{ profile_owner.id }}`)
                            .then(response => response.json())
                            .then(timeData => {
                                // Filter out days with zero time
                                const activeDays = Object.values(timeData).filter(time => time > 0).length;
                                if (activeDays > 0) {
                                    const avgTime = Math.round(data.total_time / activeDays);
                                    document.getElementById('avg-time-count').textContent = formatTime(avgTime);

                                    // Update streak days
                                    document.getElementById('streak-days').textContent = activeDays;
                                } else {
                                    document.getElementById('avg-time-count').textContent = formatTime(data.today_time);
                                    document.getElementById('streak-days').textContent = '0';
                                }
                            })
                            .catch(error => {
                                console.error("Error calculating average time:", error);
                                document.getElementById('avg-time-count').textContent = "N/A";
                            });
                    } else {
                        document.getElementById('total-time-count').textContent = "N/A";
                        document.getElementById('today-time-count').textContent = "N/A";
                        document.getElementById('avg-time-count').textContent = "N/A";
                        document.getElementById('progress-percent').textContent = "0%";
                    }
                })
                .catch(error => {
                    console.error("Error fetching time tracking data:", error);
                    document.getElementById('total-time-count').textContent = "N/A";
                    document.getElementById('today-time-count').textContent = "N/A";
                    document.getElementById('avg-time-count').textContent = "N/A";
                });
        }

        // Initial data fetch
        fetchTimeData();

        // Set up periodic refresh (every 60 seconds)
        setInterval(fetchTimeData, 60000);

        // Set up goal setting button
        const saveGoalBtn = document.getElementById('save-goal-btn');
        if (saveGoalBtn) {
            saveGoalBtn.addEventListener('click', function() {
                const goalMinutes = parseInt(document.getElementById('goal-minutes').value);
                if (isNaN(goalMinutes) || goalMinutes <= 0 || goalMinutes > 1440) {
                    alert('Please enter a valid goal between 1 and 1440 minutes.');
                    return;
                }

                fetch('/set_time_goal', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        goal_minutes: goalMinutes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Update the goal display
                        document.getElementById('daily-goal-display').textContent = `${goalMinutes} min`;

                        // Update progress ring
                        updateProgressRing(data.progress_percent);

                        // Show success message
                        const successMsg = document.createElement('div');
                        successMsg.className = 'text-sm text-green-600 mt-2';
                        successMsg.textContent = 'Goal updated successfully!';
                        document.querySelector('.flex.items-center').appendChild(successMsg);

                        // Remove success message after 3 seconds
                        setTimeout(() => {
                            if (successMsg.parentNode) {
                                successMsg.parentNode.removeChild(successMsg);
                            }
                        }, 3000);
                    } else {
                        alert('Error updating goal: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error setting goal:', error);
                    alert('Error setting goal. Please try again.');
                });
            });
        }

        // Calculate streak from time data
        const streakCacheBuster = new Date().getTime();
        fetch(`/get_daily_time_data/{{ profile_owner.id }}?_=${streakCacheBuster}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("Streak data received:", data);
                const today = new Date();
                let streak = 0;

                // Check for consecutive days with active time
                for (let i = 0; i < 100; i++) { // Check up to 100 days back
                    const checkDate = new Date(today);
                    checkDate.setDate(today.getDate() - i);
                    const dateStr = checkDate.toISOString().split('T')[0];

                    if (data[dateStr] && data[dateStr] > 0) {
                        streak++;
                    } else {
                        break; // Break on first day with no active time
                    }
                }

                // Update streak count
                document.getElementById('streak-count').textContent = streak;

                // Update streak visualization
                const streakDots = document.querySelectorAll('.rounded-full.bg-indigo-600');
                streakDots.forEach((dot, index) => {
                    if (index >= streak) {
                        dot.classList.remove('bg-indigo-600');
                        dot.classList.add('bg-gray-200');
                    }
                });
            })
            .catch(error => {
                console.error("Error fetching streak data:", error);
                document.getElementById('streak-count').textContent = '0';
            });

        // Delete Account Button Handler
        const deleteAccountBtn = document.getElementById('delete-account-btn');
        if (deleteAccountBtn) {
            deleteAccountBtn.addEventListener('click', function() {
                // Create a modal for confirmation
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
                        <h3 class="text-xl font-bold text-red-600 mb-4">Delete Account</h3>
                        <p class="text-gray-700 mb-6">Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.</p>
                        <div class="flex justify-end space-x-4">
                            <button id="cancel-delete" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors">Cancel</button>
                            <form action="/delete_account" method="POST">
                                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">Delete Account</button>
                            </form>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Add event listener to cancel button
                document.getElementById('cancel-delete').addEventListener('click', function() {
                    document.body.removeChild(modal);
                });

                // Close modal when clicking outside
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
            });
        }
    });
</script>
{% endblock %}
