{% extends 'base.html' %}

{% block title %}Edit Question{% endblock %}
{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-5xl">
        <!-- Header -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl text-center">Edit Question</h1>
            </div>
        </div>

        <!-- Question Form -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
            <div class="p-6">
                <form method="POST" enctype="multipart/form-data" class="space-y-6" id="question-form">
                    <!-- Subject and Topic Selection -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="subject" class="block text-sm font-medium leading-6 text-gray-900">Subject</label>
                            <div class="mt-2">
                                <select id="subject" name="subject_id" onchange="loadTopics(this.value)" required
                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                    <option selected disabled>Select Subject</option>
                                    {% for subject in subjects %}
                                        <option value="{{ subject.id }}" {% if subject.id == question.topic.subject_id %}selected{% endif %}>
                                            {{ subject.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="topic" class="block text-sm font-medium leading-6 text-gray-900">Topic</label>
                            <div class="mt-2">
                                <select id="topic" name="topic_id" required
                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                    <option selected disabled>Select Topic</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Question Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium leading-6 text-gray-900">Question Title</label>
                        <div class="mt-2">
                            <textarea id="title" name="title" rows="2" required
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">{{ question.title }}</textarea>
                        </div>
                    </div>

                    <!-- Source -->
                    <div>
                        <label for="source" class="block text-sm font-medium leading-6 text-gray-900">Source</label>
                        <div class="mt-2">
                            <textarea id="source" name="source" rows="2"
                                      class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">{{ question.source }}</textarea>
                        </div>
                    </div>

                    <!-- Parts -->
                    <div class="space-y-6">
                        <h2 class="text-lg font-semibold text-gray-900">Question Parts</h2>
                        {% for part in question.parts %}
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-md font-medium text-gray-900 mb-4">Part {{ loop.index }}</h3>
                            
                            <!-- Part Description with Toast UI Editor -->
                            <div class="mb-4">
                                <label for="part_{{ part.id }}_description_editor" class="block text-sm font-medium text-gray-700">Description</label>
                                <div class="mt-1">
                                    <div id="part_{{ part.id }}_description_editor" class="min-h-[200px]"></div>
                                    <input type="hidden" id="part_{{ part.id }}_description" name="part_{{ part.id }}_description" value="{{ part.description }}">
                                </div>
                            </div>

                            <!-- Part Answer -->
                            <div class="mb-4">
                                <label for="part_{{ part.id }}_answer" class="block text-sm font-medium text-gray-700">Answer</label>
                                <textarea id="part_{{ part.id }}_answer" name="part_{{ part.id }}_answer" rows="2"
                                          class="mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">{{ part.answer }}</textarea>
                            </div>

                            <!-- Part Score -->
                            <div class="mb-4">
                                <label for="part_{{ part.id }}_score" class="block text-sm font-medium text-gray-700">Score</label>
                                <input type="number" id="part_{{ part.id }}_score" name="part_{{ part.id }}_score" min="0" required
                                       value="{{ part.score }}"
                                       class="mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                            </div>

                            <!-- Part Attachment -->
                            <div class="mb-4">
                                <label for="part_{{ part.id }}_attachment" class="block text-sm font-medium text-gray-700">Attachment</label>
                                {% if part.attachment %}
                                <div class="mt-1 mb-2">
                                    <img src="{{ url_for('serve.serve_file', filename=part.attachment) }}" 
                                         class="rounded-lg max-h-[200px] w-auto"
                                         alt="Current attachment">
                                </div>
                                {% endif %}
                                <input type="file" id="part_{{ part.id }}_attachment" name="part_{{ part.id }}_attachment" accept=".jpg,.jpeg,.png,.gif"
                                       class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                            </div>

                            <!-- Marking Points -->
                            <div class="mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <label class="block text-sm font-medium text-gray-700">Marking Points</label>
                                    <div class="flex space-x-2">
                                        <button type="button" class="text-blue-600 hover:text-blue-800 text-sm generate-marking-points-btn"
                                                onclick="generateMarkingPoints({{ part.id }})">
                                            <i class="fas fa-magic"></i> Generate
                                            <span class="ml-1 loading-spinner-{{ part.id }} hidden"><i class="fas fa-spinner fa-spin"></i></span>
                                        </button>
                                        <button type="button" onclick="addMarkingPoint({{ part.id }})" class="text-green-600 hover:text-green-800 text-sm">
                                            <i class="fas fa-plus"></i> Add
                                        </button>
                                    </div>
                                </div>
                                <div id="marking_points_{{ part.id }}" class="space-y-2">
                                    {% for point in part.marking_points %}
                                    <div class="flex gap-2">
                                        <input type="text" name="part_{{ part.id }}_marking_points" value="{{ point.description }}" required
                                               class="flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                               placeholder="Marking point description">
                                        <input type="number" name="part_{{ part.id }}_marking_scores" value="{{ point.score }}" step="0.1" min="0" required
                                               class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                               placeholder="Score">
                                        <button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>  
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                                class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<link rel="stylesheet" href="https://uicdn.toast.com/editor/latest/toastui-editor.min.css" />
<script src="https://uicdn.toast.com/editor/latest/toastui-editor-all.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js" id="MathJax-script" async></script>
<script type="text/javascript">
    // Configure MathJax
    window.MathJax = {
        tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true
        },
        options: {
            skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code', '.toastui-editor-contents']
        }
    };
    
    // Function to render LaTeX in the preview pane only
    function renderMathInPreview() {
        if (window.MathJax && window.MathJax.typesetPromise) {
            // Get all preview panes
            const previewPanes = document.querySelectorAll('.toastui-editor-md-preview');
            if (previewPanes.length > 0) {
                // Only typeset the preview panes
                MathJax.typesetPromise(Array.from(previewPanes));
            }
        }
    }
    // Initialize Toast UI Editors for each part
    document.addEventListener('DOMContentLoaded', function() {
        // Trigger initial MathJax rendering after page load
        setTimeout(renderMathInPreview, 500);
        const Editor = toastui.Editor;
        
        // Initialize editors for each part description
        {% for part in question.parts %}
        const editor{{ part.id }} = new Editor({
            el: document.querySelector('#part_{{ part.id }}_description_editor'),
            height: '400px',
            initialEditType: 'markdown',
            previewStyle: 'vertical',
            initialValue: document.querySelector('#part_{{ part.id }}_description').value || ''
        });
        
        // Add event listener to render LaTeX when content changes
        editor{{ part.id }}.on('change', function() {
            setTimeout(renderMathInPreview, 100);
        });
        {% endfor %}
        
        // Update hidden inputs with editor content before form submission
        document.querySelector('#question-form').addEventListener('submit', function() {
            {% for part in question.parts %}
            document.querySelector('#part_{{ part.id }}_description').value = editor{{ part.id }}.getMarkdown();
            {% endfor %}
        });
    });

    function loadTopics(subjectId) {
        fetch('/get_topics/' + subjectId)
            .then(response => response.json())
            .then(data => {
                let topicSelect = document.getElementById('topic');
                topicSelect.innerHTML = "<option selected disabled>Select Topic</option>";
                data.topics.forEach(function(topic) {
                    let option = document.createElement("option");
                    option.value = topic.id;
                    option.text = topic.name;
                    if (topic.id == {{ question.topic_id }}) {
                        option.selected = true;
                    }
                    topicSelect.appendChild(option);
                });
            });
    }

    function addMarkingPoint(partId) {
        const container = document.getElementById("marking_points_" + partId);
        const div = document.createElement('div');
        div.className = 'flex gap-2';
        div.innerHTML = '<input type="text" name="part_' + partId + '_marking_points" required ' +
                   'class="flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" ' +
                   'placeholder="Marking point description">' +
            '<input type="number" name="part_' + partId + '_marking_scores" step="0.1" min="0" required ' +
                   'class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" ' +
                   'placeholder="Score">' +
            '<button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">' +
                '<i class="fas fa-trash"></i>' +
            '</button>';
        container.appendChild(div);
    }

    function removeMarkingPoint(button) {
        button.closest('div').remove();
    }
    
    // Generate marking points via API
    async function generateMarkingPoints(partId) {
        const descriptionInput = document.querySelector('#part_' + partId + '_description');
        const answerTextarea = document.getElementById("part_" + partId + "_answer");
        const scoreInput = document.getElementById("part_" + partId + "_score");
        const mpContainer = document.getElementById("marking_points_" + partId);
        const loadingSpinner = document.querySelector(".loading-spinner-" + partId);
        
        // Get the editor instance for this part
        const editorInstance = window['editor' + partId];
        // Update the hidden input with the current editor content
        if (editorInstance) {
            descriptionInput.value = editorInstance.getMarkdown();
        }

        if (!descriptionInput || !answerTextarea || !scoreInput || !mpContainer) {
            console.error("Could not find necessary elements for part:", partId);
            alert("Error: Could not find elements for this part.");
            return;
        }

        const partDescription = descriptionInput.value;
        const partAnswer = answerTextarea.value;
        const partScore = scoreInput.value;

        if (!partDescription || !partAnswer || !partScore) {
            alert("Please ensure the part description, answer, and score are filled before generating marking points.");
            return;
        }

        // Show loading state
        const generateButton = loadingSpinner.parentElement;
        generateButton.disabled = true;
        loadingSpinner.classList.remove('hidden');

        try {
            const response = await fetch('/generate_marking_points', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    partDescription: partDescription,
                    partAnswer: partAnswer,
                    partScore: parseInt(partScore) // Ensure score is sent as a number
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: 'Unknown server error' }));
                throw new Error("Server error: " + response.status + " - " + (errorData.message || 'Failed to generate marking points'));
            }

            const generatedPoints = await response.json();

            if (!Array.isArray(generatedPoints)) {
                throw new Error("Invalid response format from server. Expected an array.");
            }

            // Clear existing marking points
            mpContainer.innerHTML = '';

            // Add new marking points from response
            generatedPoints.forEach(function(mp) {
                const div = document.createElement('div');
                div.className = 'flex gap-2';
                div.innerHTML = '<input type="text" name="part_' + partId + '_marking_points" value="' + (mp.description || '') + '" required ' +
                       'class="flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" ' +
                       'placeholder="Marking point description">' +
                '<input type="number" name="part_' + partId + '_marking_scores" value="' + (mp.score || 0) + '" step="0.1" min="0" required ' +
                       'class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" ' +
                       'placeholder="Score">' +
                '<button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">' +
                    '<i class="fas fa-trash"></i>' +
                '</button>';
                mpContainer.appendChild(div);
            });

        } catch (error) {
            console.error('Error generating marking points:', error);
            alert("Failed to generate marking points: " + error.message);
        } finally {
            // Hide loading state
            generateButton.disabled = false;
            loadingSpinner.classList.add('hidden');
        }
    }

    // Load topics for the selected subject on page load
    document.addEventListener('DOMContentLoaded', function() {
        const subjectSelect = document.getElementById('subject');
        if (subjectSelect.value) {
            loadTopics(subjectSelect.value);
        }
    });
</script>
{% endblock %}
