{% extends "base.html" %}

{% block content %}
<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <i class="fab fa-google text-green-600 text-2xl"></i>
            </div>
            <h1 class="text-4xl font-bold tracking-tight text-gray-900">Almost There!</h1>
            <p class="mt-2 text-sm text-gray-600">
                Choose a username to complete your Google account setup
            </p>
            {% if google_data and google_data.email %}
            <p class="mt-1 text-xs text-gray-500">
                Signing up with: {{ google_data.email }}
            </p>
            {% endif %}
        </div>

        <!-- Username Selection Form -->
        <div class="mt-8 bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transform transition-all hover:scale-[1.01] duration-300">
            <form method="POST" action="/choose_username" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium leading-6 text-gray-900">
                        Choose Your Username
                    </label>
                    <div class="mt-2 relative rounded-md shadow-sm">
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="username" 
                               name="username" 
                               required
                               value="{{ suggested_username }}"
                               class="block w-full rounded-md border-0 py-3 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                               placeholder="Enter your username">
                    </div>
                    <p class="mt-2 text-xs text-gray-500">
                        Username must be at least 3 characters and can contain letters, numbers, hyphens, and underscores.
                    </p>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Account Information:</h4>
                    <div class="space-y-1 text-sm text-gray-600">
                        {% if google_data and google_data.name %}
                        <p><span class="font-medium">Name:</span> {{ google_data.name }}</p>
                        {% endif %}
                        {% if google_data and google_data.email %}
                        <p><span class="font-medium">Email:</span> {{ google_data.email }}</p>
                        {% endif %}
                        <p><span class="font-medium">Account Type:</span> Google Sign-in</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <button type="submit"
                            class="group relative flex w-full justify-center rounded-md bg-indigo-600 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200">
                        <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                            <i class="fas fa-check text-indigo-300 group-hover:text-indigo-200"></i>
                        </span>
                        Create Account
                    </button>

                    <div class="text-center">
                        <a href="{{ url_for('cancel_google_signup') }}"
                           class="text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200">
                            <i class="fas fa-times mr-1"></i>
                            Cancel Google Signup
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Username Availability Check -->
        <div id="usernameStatus" class="hidden mt-2 text-sm">
            <div id="usernameAvailable" class="text-green-600 hidden">
                <i class="fas fa-check-circle mr-1"></i>
                Username is available!
            </div>
            <div id="usernameTaken" class="text-red-600 hidden">
                <i class="fas fa-times-circle mr-1"></i>
                Username is already taken.
            </div>
            <div id="usernameChecking" class="text-gray-500 hidden">
                <i class="fas fa-spinner fa-spin mr-1"></i>
                Checking availability...
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const usernameInput = document.getElementById('username');
    const usernameStatus = document.getElementById('usernameStatus');
    const usernameAvailable = document.getElementById('usernameAvailable');
    const usernameTaken = document.getElementById('usernameTaken');
    const usernameChecking = document.getElementById('usernameChecking');
    
    let checkTimeout;
    
    usernameInput.addEventListener('input', function() {
        const username = this.value.trim();
        
        // Clear previous timeout
        clearTimeout(checkTimeout);
        
        // Hide all status messages
        usernameStatus.classList.add('hidden');
        usernameAvailable.classList.add('hidden');
        usernameTaken.classList.add('hidden');
        usernameChecking.classList.add('hidden');
        
        if (username.length >= 3) {
            // Show checking status
            usernameStatus.classList.remove('hidden');
            usernameChecking.classList.remove('hidden');
            
            // Debounce the check
            checkTimeout = setTimeout(() => {
                checkUsernameAvailability(username);
            }, 500);
        }
    });
    
    function checkUsernameAvailability(username) {
        fetch('/api/check_username', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username: username })
        })
        .then(response => response.json())
        .then(data => {
            usernameChecking.classList.add('hidden');
            
            if (data.available) {
                usernameAvailable.classList.remove('hidden');
            } else {
                usernameTaken.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error checking username:', error);
            usernameChecking.classList.add('hidden');
        });
    }
    
    // Auto-focus on username input
    usernameInput.focus();
    usernameInput.select();
});
</script>

{% endblock %}
