{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Group Header with Animated Background -->
    <div class="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl overflow-hidden mb-8 shadow-lg transform transition-all duration-300 hover:shadow-xl hover:scale-[1.01]">
        <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
        <div class="relative p-8 md:p-10">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="text-center md:text-left mb-6 md:mb-0">
                    <h1 class="text-3xl md:text-4xl font-bold text-white tracking-tight">{{ group.name }}</h1>
                    {% if group.description %}
                    <p class="mt-2 text-indigo-100 max-w-2xl">{{ group.description }}</p>
                    {% endif %}
                </div>
                
                <div class="flex flex-col sm:flex-row gap-4 items-center">
                    <div class="flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 text-white">
                        <i class="fas fa-users mr-2"></i>
                        <span class="font-medium">{{ group.members|length }} members</span>
                    </div>
                    
                    {% if session.user_id and User.query.get(session.user_id) in group.members %}
                    <form action="{{ url_for('leave_group', group_id=group.id) }}" method="POST" class="inline">
                        <button type="submit" 
                                class="group relative inline-flex items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm px-4 py-2 text-white font-medium overflow-hidden transition-all duration-300 hover:bg-white/20"
                                onclick="return confirm('Are you sure you want to leave this group?')">
                            <span class="relative flex items-center">
                                <i class="fas fa-sign-out-alt mr-2 transition-transform duration-300 group-hover:-translate-x-1"></i>
                                Leave Group
                            </span>
                        </button>
                    </form>
                    {% endif %}

                    <!-- Delete Group Button (Only for Owner) -->
                    {% if session.user_id == group.owner_id %}
                    <form action="{{ url_for('delete_group', group_id=group.id) }}" method="POST" class="inline">
                        <button type="submit" 
                                class="group relative inline-flex items-center justify-center rounded-lg bg-red-600/10 backdrop-blur-sm px-4 py-2 text-red-600 font-medium overflow-hidden transition-all duration-300 hover:bg-red-600/20"
                                onclick="return confirm('Are you sure you want to DELETE this group? This action cannot be undone.')">
                            <span class="relative flex items-center">
                                <i class="fas fa-trash-alt mr-2 transition-transform duration-300 group-hover:scale-110"></i>
                                Delete Group
                            </span>
                        </button>
                    </form>
                    {% endif %}
                    <!-- End Delete Group Button -->
                </div>
            </div>
        </div>
    </div>

    <!-- Group Leaderboard -->
    <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
        <!-- Main Leaderboard -->
        <div class="lg:col-span-8">
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                        <i class="fas fa-trophy text-amber-500 mr-2"></i>
                        Group Leaderboard
                    </h2>
                    
                    <div class="overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Member</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Problems</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                                        <th class="px-6 py-3"></th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    {% for member in sorted_members %}
                                    <tr class="group transition-colors duration-200 hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            {% if loop.index == 1 %}
                                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-amber-100 text-amber-600">
                                                    <i class="fas fa-trophy"></i>
                                                </div>
                                            {% elif loop.index == 2 %}
                                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-600">
                                                    <i class="fas fa-medal"></i>
                                                </div>
                                            {% elif loop.index == 3 %}
                                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-orange-100 text-orange-600">
                                                    <i class="fas fa-award"></i>
                                                </div>
                                            {% else %}
                                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-indigo-50 text-indigo-600">
                                                    <span class="text-sm font-medium">{{ loop.index }}</span>
                                                </div>
                                            {% endif %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-medium">
                                                    {{ member.user.username[:1].upper() }}
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ member.user.username }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                {{ member.problems_completed }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            <span class="text-sm font-medium text-gray-900">{{ member.total_points|round|int }}</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            <a href="{{ url_for('user_profile', username=member.user.username) }}" 
                                               class="inline-flex items-center rounded-md bg-white px-3 py-1.5 text-sm font-medium text-indigo-600 shadow-sm ring-1 ring-inset ring-indigo-200 transition-all duration-200 hover:bg-indigo-50 hover:text-indigo-700">
                                                <span>Profile</span>
                                                <i class="fas fa-chevron-right ml-1.5 text-xs opacity-70 transition-transform duration-200 group-hover:translate-x-0.5"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Stats and Top Performers -->
        <div class="lg:col-span-4 space-y-8">
            <!-- Group Stats -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-chart-line text-indigo-500 mr-2"></i>
                        Group Stats
                    </h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-indigo-100 text-indigo-600 mr-3">
                                    <i class="fas fa-users"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700">Total Members</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">{{ group.members|length }}</span>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600 mr-3">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700">Problems Solved</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">{{ sorted_members|sum(attribute='problems_completed') }}</span>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 text-purple-600 mr-3">
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700">Total Points</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">{{ sorted_members|sum(attribute='total_points')|round|int }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Top Performers -->
            {% if sorted_members and sorted_members|length > 0 %}
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-crown text-amber-500 mr-2"></i>
                        Top Performer
                    </h2>
                    
                    {% set top_member = sorted_members[0] %}
                    <div class="flex flex-col items-center p-4 bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg border border-amber-200">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-br from-amber-500 to-amber-600 flex items-center justify-center text-white text-xl font-bold mb-3">
                            {{ top_member.user.username[:1].upper() }}
                        </div>
                        <h3 class="text-lg font-medium text-gray-900">{{ top_member.user.username }}</h3>
                        <div class="mt-2 flex items-center space-x-1 text-amber-600">
                            <i class="fas fa-trophy"></i>
                            <span class="font-medium">Group Leader</span>
                        </div>
                        <div class="mt-4 grid grid-cols-2 gap-3 w-full">
                            <div class="flex flex-col items-center p-2 bg-white rounded-md shadow-sm">
                                <span class="text-sm text-gray-500">Problems</span>
                                <span class="text-lg font-semibold text-gray-900">{{ top_member.problems_completed }}</span>
                            </div>
                            <div class="flex flex-col items-center p-2 bg-white rounded-md shadow-sm">
                                <span class="text-sm text-gray-500">Points</span>
                                <span class="text-lg font-semibold text-gray-900">{{ top_member.total_points|round|int }}</span>
                            </div>
                        </div>
                        <a href="{{ url_for('user_profile', username=top_member.user.username) }}" 
                           class="mt-4 inline-flex items-center rounded-md bg-amber-50 px-3 py-2 text-sm font-medium text-amber-700 shadow-sm ring-1 ring-inset ring-amber-600/20 hover:bg-amber-100 transition-colors duration-200">
                            View Profile
                            <i class="fas fa-chevron-right ml-1.5 text-xs"></i>
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <div class="mt-8 text-center">
        <a href="{{ url_for('groups') }}" class="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Groups
        </a>
    </div>
</div>

<style>
    /* Animated background grid */
    .bg-grid-white {
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    }
    
    /* Subtle hover animations */
    .hover-lift {
        transition: transform 0.2s ease;
    }
    
    .hover-lift:hover {
        transform: translateY(-2px);
    }
    
    /* Smooth transitions */
    .transition-all {
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
    }
    
    /* Custom scrollbar for tables */
    .overflow-x-auto::-webkit-scrollbar {
        height: 6px;
    }
    
    .overflow-x-auto::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 3px;
    }
    
    .overflow-x-auto::-webkit-scrollbar-thumb {
        background-color: #cbd5e1;
        border-radius: 3px;
    }
    
    .overflow-x-auto::-webkit-scrollbar-thumb:hover {
        background-color: #94a3b8;
    }
</style>

<script>
    // Add subtle animations when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Animate elements as they come into view
        const animateOnScroll = function() {
            const elements = document.querySelectorAll('.rounded-xl');
            
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('animate-fade-in');
                }
            });
        };
        
        // Initial check
        animateOnScroll();
        
        // Check on scroll
        window.addEventListener('scroll', animateOnScroll);
    });
    
    // Add animation class
    document.head.insertAdjacentHTML('beforeend', `
        <style>
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            .animate-fade-in {
                animation: fadeIn 0.5s ease forwards;
            }
            
            .rounded-xl {
                opacity: 0;
            }
            
            .rounded-xl:nth-child(1) { animation-delay: 0.1s; }
            .rounded-xl:nth-child(2) { animation-delay: 0.2s; }
            .rounded-xl:nth-child(3) { animation-delay: 0.3s; }
        </style>
    `);
</script>
{% endblock %}
