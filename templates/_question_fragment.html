<div id="question-container">
    <form action="{{ url_for('submit_ocr_data', question_id=cur_idx) }}" method="POST" id="question-answer-form">
        <strong> Question {{ cur_idx+1 }} of {{ max_idx+1 }} </strong>

        <!-- Error and Warning Messages Template -->
        {% if errors or warnings %}
        <div class="mt-4 mb-6">
            {% if errors and errors[cur_idx] %}
            <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded-md">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-500"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Errors that need to be fixed on this page:</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                {% for error in errors[cur_idx] %}
                                <li id="error-{{ error.id }}">{{ error.message }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% if warnings and warnings[cur_idx] %}
            <div class="bg-yellow-50 border-l-4 border-yellow-500 p-4 rounded-md">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Warnings to consider:</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc pl-5 space-y-1">
                                {% for warning in warnings[cur_idx] %}
                                <li id="warning-{{ warning.id }}">{{ warning.message }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}
        <div class="mb-4">
            <label for="topic_id" class="block text-sm font-medium text-gray-700">Topic for this Question
                (Optional)</label>
            <div class="mt-2">
                <select id="topic_id" name="topic_id"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm
                    {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'topic_id')|list %}
                    border-yellow-500 bg-yellow-50
                    {% endif %}
                    {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'topic_id')|list %}
                    border-red-500 bg-red-50
                    {% endif %}">
                    {% if topic_id %}
                    <option selected value="{{ topic_id }}">{{ topic_name }}</option>
                    {% else %}
                    <option selected value="">-- No Topic --</option>
                    {% endif %}
                    {% for topic in topics %}
                    {% if topic.id != topic_id %}
                        <option value="{{ topic.id }}">{{ topic.name }}</option>
                    {% endif %}
                    {% endfor %}
                </select>
                {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'topic_id')|list %}
                <p class="mt-1 text-xs text-yellow-600">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    {{ warnings[cur_idx]|selectattr('id', 'equalto', 'topic_id')|map(attribute='message')|first }}
                </p>
                {% endif %}
                {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'topic_id')|list %}
                <p class="mt-1 text-xs text-red-600">
                    <i class="fas fa-exclamation-circle mr-1"></i>
                    {{ errors[cur_idx]|selectattr('id', 'equalto', 'topic_id')|map(attribute='message')|first }}
                </p>
                {% endif %}
            </div>
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700">Source</label>
            <input type="text" name="source" value="{{ source|default() }}"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm
                {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'source')|list %}
                border-yellow-500 bg-yellow-50
                {% endif %}
                {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'source')|list %}
                border-red-500 bg-red-50
                {% endif %}">
            {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'source')|list %}
            <p class="mt-1 text-xs text-yellow-600">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                {{ warnings[cur_idx]|selectattr('id', 'equalto', 'source')|map(attribute='message')|first }}
            </p>
            {% endif %}
            {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'source')|list %}
            <p class="mt-1 text-xs text-red-600">
                <i class="fas fa-exclamation-circle mr-1"></i>
                {{ errors[cur_idx]|selectattr('id', 'equalto', 'source')|map(attribute='message')|first }}
            </p>
            {% endif %}
        </div>

        <div class="mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Parts</h3>
            <div class="parts-container">
                {% for part in question_data %}
                {% set part_index = loop.index0 %}
                <div class="part-item bg-gray-50 p-4 rounded-md mb-4" data-part-index="{{ part_index }}">
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="font-medium text-gray-700">Part {{ loop.index }}</h4>
                        <button type="button" class="text-red-600 hover:text-red-800 remove-part-btn"
                                onclick="removePart(this)">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label for="description-{{ part_index }}" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea name="description-{{ part_index }}" id="description-{{ part_index }}" class="latex-content block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm" rows="4">{{ part.description }}</textarea>
                            <div id="preview-description-{{ part_index }}" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                            <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                                <span>Supports Markdown and LaTeX (use $ for inline math, $$ for display math)</span>
                                <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                        onclick="document.getElementById('description-{{ loop.index }}').dispatchEvent(new Event('input'))">
                                    Refresh Preview
                                </button>
                            </div>
                        </div>

                        <div>
                            <label for="answer-{{ part_index }}" class="block text-sm font-medium text-gray-700">Answer</label>
                            <textarea name="answer-{{ part_index }}" id="answer-{{ part_index }}" class="latex-content block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm" rows="4">{{ part.answer }}</textarea>
                            <div id="preview-answer-{{ part_index }}" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                            <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                                <span>Supports Markdown and LaTeX (use \$ for inline math, \$$ for display math)</span>
                                <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                        onclick="document.getElementById('answer-{{ loop.index }}').dispatchEvent(new Event('input'))">
                                    Refresh Preview
                                </button>
                            </div>
                        </div>

                        <div>
                            <label for="score-{{ part_index }}" class="block text-sm font-medium text-gray-700">Score</label>
                            <input type="number" name="score-{{ part_index }}"
                                value="{{ part.score|default(part.score) }}" min="1"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm
                                {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'score-' ~ part_index)|list %}
                                border-yellow-500 bg-yellow-50
                                {% endif %}
                                {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'score-' ~ part_index)|list %}
                                border-red-500 bg-red-50
                                {% endif %}">
                            {% if warnings and warnings[cur_idx] and warnings[cur_idx]|selectattr('id', 'equalto', 'score-' ~ part_index)|list %}
                            <p class="mt-1 text-xs text-yellow-600">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                {{ warnings[cur_idx]|selectattr('id', 'equalto', 'score-' ~ part_index)|map(attribute='message')|first }}
                            </p>
                            {% endif %}
                            {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'score-' ~ part_index)|list %}
                            <p class="mt-1 text-xs text-red-600">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ errors[cur_idx]|selectattr('id', 'equalto', 'score-' ~ part_index)|map(attribute='message')|first }}
                            </p>
                            {% endif %}
                        </div>

                        <!-- Display Question Type and Options -->
                        <div class="mt-4">
                            <p class="block text-sm font-medium text-gray-700">Part Type: 
                                <span class="font-normal">{{ part.type | default('text') | capitalize }}</span>
                            </p>
                        </div>

                        {% if part.type == 'mcq' %}
                        <div class="mt-2 mb-4 mcq-options-section">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Options</label>
                            <div class="mcq-options-container space-y-2" id="mcq-options-container-{{ part_index }}">
                                {% if part.options %}
                                    {% for option in part.options %}
                                    {% set option_index = loop.index0 %}
                                    <div class="mcq-option-item flex items-center space-x-2" data-option-index="{{ option_index }}">
                                        <input type="radio" name="part-{{ part_index }}-correct-answer" 
                                               value="{{ option.text if option is mapping else option }}"
                                               id="part-{{ part_index }}-option-{{ option_index }}-radio"
                                               {% if part.answer and ((option is mapping and option.text == part.answer) or option == part.answer) %}checked{% endif %}
                                               class="form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out">
                                        <input type="text" name="part-{{ part_index }}-option-{{ option_index }}-text"
                                               value="{{ option.text if option is mapping else option }}"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                               oninput="updateRadioValue(this, 'part-{{ part_index }}-option-{{ option_index }}-radio')">
                                        <button type="button" class="text-red-600 hover:text-red-800 remove-mcq-option-btn p-1"
                                                onclick="removeMcqOption(this, '{{ part_index }}')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <button type="button" class="text-indigo-600 hover:text-indigo-800 text-sm mt-2 add-mcq-option-btn"
                                    onclick="addMcqOption('{{ part_index }}')">
                                <i class="fas fa-plus mr-1"></i> Add Option
                            </button>
                        </div>
                        {% endif %}
                        <!-- End Display Question Type and Options -->

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">Attachment</label>
                            {% if part.attachments %}
                            {% for attachment, show in part.attachments.items() %}
                            <div class="mt-1 mb-2">
                                <img src="{{ url_for('serve.serve_file', filename=attachment) }}" class="rounded-lg max-h-[200px] w-auto"
                                    alt="Part attachment">
                                <input type="checkbox" name="{{ attachment }}-{{ part_index }}" {% if show %} checked {% endif %}>
                            </div>
                            {% endfor %}
                            {% endif %}
                        </div>

                        <div class="marking-points-section">
                            <div class="flex justify-between items-center">
                                <label class="block text-sm font-medium text-gray-700">Marking Points</label>
                                <div class="flex space-x-2">
                                    <button type="button" class="text-green-600 hover:text-green-800 text-sm add-marking-point-btn"
                                            onclick="addMarkingPoint(this, '{{ part_index }}')">
                                        <i class="fas fa-plus"></i> Add
                                    </button>
                                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm add-marking-point-btn"
                                    hx-post="{{ url_for('get_marking_points') }}"
                                    hx-target="#marking-points-container-{{ part_index }}"
                                    hx-vals='{"part_id": "{{ part_index }}", "question_id": "{{ cur_idx }}"}'
                                    hx-swap="innerHTML"
                                        >
                                        <i class="fas fa-plus"></i> Generate (Gemini)
                                    </button>
                                </div>
                            </div>

                            <div class="marking-points-container mt-2" id="marking-points-container-{{ part_index }}">
                                {% if part.marking_points and part.marking_points|length > 0 %}
                                    {% with part=part, part_index=part_index %}{% include "_marking_points.html" %}{% endwith %}
                                {% else %}
                                    <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                        <p class="text-xs text-yellow-700 flex items-center">
                                            <i class="fas fa-exclamation-triangle mr-2 text-yellow-500"></i>
                                            No marking points generated for this part
                                        </p>
                                    </div>
                                {% endif %}
                            </div>

                            {% if errors and errors[cur_idx] and errors[cur_idx]|selectattr('id', 'equalto', 'marking-points-container-' ~ part_index)|list %}
                                <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                                    <p class="text-xs text-red-700 flex items-center">
                                        <i class="fas fa-exclamation-circle mr-2 text-red-500"></i>
                                        {{ errors[cur_idx]|selectattr('id', 'equalto', 'marking-points-container-' ~ part_index)|map(attribute='message')|first }}
                                    </p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <div class="mt-4">
                <button type="button" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        onclick="addPart()">
                    <i class="fas fa-plus mr-2"></i> Add Part
                </button>
            </div>
        </div>

    <div class="pagination-controls mt-8 flex items-center justify-between">
        <button
            {% if cur_idx==0 %} disabled {% endif %}
            hx-post="{{ url_for('get_question_html', question_id=cur_idx) }}"
            hx-target="#question-container"
            hx-swap="innerHTML"
            hx-include="#question-answer-form"
            hx-vals='{"target_index": {{ cur_idx-1 }}}'
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
            <i class="fas fa-arrow-left mr-2"></i> Previous
        </button>

        <button type="submit"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
            <i class="fas fa-save mr-2"></i> Save
        </button>

        <button
            {% if cur_idx==max_idx %} disabled {% endif %}
            hx-post="{{ url_for('get_question_html', question_id=cur_idx) }}"
            hx-target="#question-container"
            hx-swap="innerHTML"
            hx-include="#question-answer-form"
            hx-vals='{"target_index": {{ cur_idx+1 }}}'
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
            Next <i class="fas fa-arrow-right ml-2"></i>
        </button>
    </div>
    </form>
</div>

<script>
    // Function to add a new part
    function addPart() {
        const partsContainer = document.querySelector('.parts-container');
        const partCount = partsContainer.querySelectorAll('.part-item').length;
        const newPartIndex = partCount;

        // Create a new part element
        const newPart = document.createElement('div');
        newPart.className = 'part-item bg-gray-50 p-4 rounded-md mb-4';
        newPart.setAttribute('data-part-index', newPartIndex);
        newPart.innerHTML = `
            <div class="flex justify-between items-center mb-2">
                <h4 class="font-medium text-gray-700">Part ${partCount + 1}</h4>
                <button type="button" class="text-red-600 hover:text-red-800 remove-part-btn"
                        onclick="removePart(this)">
                    <i class="fas fa-trash"></i> Remove
                </button>
            </div>

            <div class="space-y-4">
                <div>
                    <label for="description-${partCount + 1}" class="block text-sm font-medium text-gray-700">Description</label>
                    <textarea name="description-${partCount + 1}" id="description-${partCount + 1}" class="latex-content block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm" rows="4"></textarea>
                    <div id="preview-description-${partCount + 1}" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                    <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                        <span>Supports Markdown and LaTeX (use $ for inline math, $$ for display math)</span>
                        <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                onclick="document.getElementById('description-${partCount + 1}').dispatchEvent(new Event('input'))">
                            Refresh Preview
                        </button>
                    </div>
                </div>

                <div>
                    <label for="answer-${partCount + 1}" class="block text-sm font-medium text-gray-700">Answer</label>
                    <textarea name="answer-${partCount + 1}" id="answer-${partCount + 1}" class="latex-content block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm" rows="4"></textarea>
                    <div id="preview-answer-${partCount + 1}" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                    <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                        <span>Supports Markdown and LaTeX (use $ for inline math, $$ for display math)</span>
                        <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                onclick="document.getElementById('answer-${partCount + 1}').dispatchEvent(new Event('input'))">
                            Refresh Preview
                        </button>
                    </div>
                </div>

                <div>
                    <label for="score-${partCount + 1}" class="block text-sm font-medium text-gray-700">Score</label>
                    <input type="number" name="score-${partCount + 1}"
                        value="1" min="1"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    <!-- Error/warning messages will be added here if needed -->
                </div>

                <div class="marking-points-section">
                    <div class="flex justify-between items-center">
                        <label class="block text-sm font-medium text-gray-700">Marking Points</label>
                        <div class="flex space-x-2">
                            <button type="button" class="text-green-600 hover:text-green-800 text-sm add-marking-point-btn"
                                    onclick="addMarkingPoint(this, '${newPartIndex}')">
                                <i class="fas fa-plus"></i> Add
                            </button>
                        </div>
                    </div>

                    <div class="marking-points-container mt-2" id="marking-points-container-${newPartIndex}">
                        <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                            <p class="text-xs text-yellow-700 flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2 text-yellow-500"></i>
                                No marking points generated for this part
                            </p>
                        </div>
                    </div>
                    <!-- Error message for sum of marking point scores will be displayed here if needed -->
                </div>
            </div>
        `;

        // Append the new part to the container
        partsContainer.appendChild(newPart);

        // Initialize the new textareas for live preview
        const newDescriptionTextarea = document.getElementById(`description-${partCount + 1}`);
        const newAnswerTextarea = document.getElementById(`answer-${partCount + 1}`);

        if (newDescriptionTextarea) {
            newDescriptionTextarea.addEventListener('input', function() {
                updatePreview(this);
            });
        }

        if (newAnswerTextarea) {
            newAnswerTextarea.addEventListener('input', function() {
                updatePreview(this);
            });
        }

        // Renumber parts for consistency
        renumberParts();
    }

    // Function to remove a part
    function removePart(button) {
        if (confirm('Are you sure you want to remove this part?')) {
            const partItem = button.closest('.part-item');
            partItem.remove();

            // Renumber the parts
            renumberParts();
        }
    }

    // Function to add a new marking point
    function addMarkingPoint(button, partIndex) {
        // Convert partIndex to string if it's not already
        partIndex = String(partIndex);

        const mpContainer = button.closest('.marking-points-section').querySelector('.marking-points-container');

        // Check if there's a warning message and remove it
        const warningMessage = mpContainer.querySelector('.bg-yellow-50');
        if (warningMessage) {
            warningMessage.remove();
        }

        // Don't remove error messages about sum of marking point scores

        const mpCount = mpContainer.querySelectorAll('.marking-point-item').length;

        const newMarkingPoint = document.createElement('div');
        newMarkingPoint.className = 'marking-point-item p-2 border border-gray-200 rounded mb-2';
        newMarkingPoint.innerHTML = `
            <div class="flex items-center justify-between mb-2">
                <span class="marking-point-badge">Point ${mpCount + 1}</span>
                <button type="button" class="text-red-600 hover:text-red-800 remove-marking-point-btn"
                        onclick="removeMarkingPoint(this)">
                    <i class="fas fa-times"></i> Remove
                </button>
            </div>
            <div class="flex flex-col space-y-2">
                <div>
                    <label class="block text-xs font-medium text-gray-500">Description</label>
                    <div class="mt-1">
                        <textarea name="marking-points-${partIndex}-${mpCount}-description"
                                class="latex-content block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm" rows="2"></textarea>
                        <div class="mt-2 p-2 bg-gray-50 rounded latex-preview" id="latex-preview-${partIndex}-${mpCount}"></div>
                        <!-- Warning messages for description will be displayed here if needed -->
                    </div>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-500">Score</label>
                    <input type="number" name="marking-points-${partIndex}-${mpCount}-score"
                            value="1" placeholder="Score" step="0.1" min="0"
                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    <!-- Error messages for score will be displayed here if needed -->
                </div>
            </div>
        `;

        mpContainer.appendChild(newMarkingPoint);

        // Initialize LaTeX rendering for the new marking point
        const textarea = newMarkingPoint.querySelector('.latex-content');
        const previewDiv = newMarkingPoint.querySelector('.latex-preview');

        if (textarea && previewDiv) {
            textarea.addEventListener('input', function() {
                previewDiv.textContent = this.value;
                renderMathInElement(previewDiv, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\(', right: '\\)', display: false},
                        {left: '\\[', right: '\\]', display: true}
                    ],
                    throwOnError: false,
                    output: 'html'
                });
            });
        }

        // Renumber all marking points for consistency
        renumberMarkingPoints(partIndex);
    }

    // Function to remove a marking point
    function removeMarkingPoint(button) {
        const markingPointItem = button.closest('.marking-point-item');
        const markingPointsContainer = markingPointItem.closest('.marking-points-container');
        const partItem = markingPointItem.closest('.part-item');
        const partIndex = partItem.getAttribute('data-part-index');

        markingPointItem.remove();

        // Check if there are no more marking points, and if so, add the warning message
        const remainingMarkingPoints = markingPointsContainer.querySelectorAll('.marking-point-item');
        if (remainingMarkingPoints.length === 0) {
            const warningMessage = document.createElement('div');
            warningMessage.className = 'p-3 bg-yellow-50 border border-yellow-200 rounded-md';
            warningMessage.innerHTML = `
                <p class="text-xs text-yellow-700 flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2 text-yellow-500"></i>
                    No marking points generated for this part
                </p>
            `;
            markingPointsContainer.appendChild(warningMessage);
        }

        // Renumber the marking points
        renumberMarkingPoints(partIndex);
    }

    // Function to renumber parts
    function renumberParts() {
        const parts = document.querySelectorAll('.part-item');
        parts.forEach((part, index) => {
            // Update part index attribute
            part.setAttribute('data-part-index', index);

            // Update part title
            const partTitle = part.querySelector('h4');
            if (partTitle) {
                partTitle.textContent = `Part ${index + 1}`;
            }

            // Update form field names
            const descriptionTextarea = part.querySelector('textarea[id^="description-"]');
            const answerTextarea = part.querySelector('textarea[id^="answer-"]');
            const scoreInput = part.querySelector('input[name^="score-"]');

            if (descriptionTextarea) {
                descriptionTextarea.name = `description-${index + 1}`;
                descriptionTextarea.id = `description-${index + 1}`;
            }

            if (answerTextarea) {
                answerTextarea.name = `answer-${index + 1}`;
                answerTextarea.id = `answer-${index + 1}`;
            }

            if (scoreInput) {
                scoreInput.name = `score-${index + 1}`;
            }

            // Update preview div IDs
            const descriptionPreview = part.querySelector('div[id^="preview-description-"]');
            const answerPreview = part.querySelector('div[id^="preview-answer-"]');

            if (descriptionPreview) {
                descriptionPreview.id = `preview-description-${index + 1}`;
            }

            if (answerPreview) {
                answerPreview.id = `preview-answer-${index + 1}`;
            }

            // Update marking points container ID
            const mpContainer = part.querySelector('.marking-points-container');
            if (mpContainer) {
                mpContainer.id = `marking-points-container-${index}`;
            }

            // Update add marking point button
            const addMpButton = part.querySelector('.add-marking-point-btn');
            if (addMpButton) {
                addMpButton.setAttribute('onclick', `addMarkingPoint(this, ${index})`);
            }

            // Renumber marking points
            renumberMarkingPoints(index);
        });
    }

    // Function to renumber marking points for a specific part
    function renumberMarkingPoints(partIndex) {
        // Convert partIndex to string if it's not already
        partIndex = String(partIndex);

        const mpContainer = document.getElementById(`marking-points-container-${partIndex}`);
        if (!mpContainer) return;

        const markingPoints = mpContainer.querySelectorAll('.marking-point-item');
        markingPoints.forEach((mp, mpIndex) => {
            // Update point number
            const pointBadge = mp.querySelector('.marking-point-badge');
            if (pointBadge) {
                pointBadge.textContent = `Point ${mpIndex + 1}`;
            }

            // Update form field names
            const descriptionTextarea = mp.querySelector('textarea');
            const scoreInput = mp.querySelector('input[type="number"]');

            if (descriptionTextarea) {
                descriptionTextarea.name = `marking-points-${partIndex}-${mpIndex}-description`;
            }

            if (scoreInput) {
                scoreInput.name = `marking-points-${partIndex}-${mpIndex}-score`;
            }
        });
    }

    // Function to update radio button value when text input changes
    function updateRadioValue(textInput, radioId) {
        const radioElement = document.getElementById(radioId);
        if (radioElement) {
            radioElement.value = textInput.value;
        }
    }

    // Function to add a new MCQ option
    function addMcqOption(partIndex) {
        const optionsContainer = document.getElementById(`mcq-options-container-${partIndex}`);
        if (!optionsContainer) return;

        const optionCount = optionsContainer.querySelectorAll('.mcq-option-item').length;
        const newOptionIndex = optionCount; // This will be the index for the new option

        const newOptionDiv = document.createElement('div');
        newOptionDiv.className = 'mcq-option-item flex items-center space-x-2'; // Removed mb-2, handled by space-y-2 on container
        newOptionDiv.setAttribute('data-option-index', newOptionIndex);

        newOptionDiv.innerHTML = `
            <input type="radio" name="part-${partIndex}-correct-answer" value="New Option ${newOptionIndex + 1}"
                   id="part-${partIndex}-option-${newOptionIndex}-radio"
                   class="form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out">
            <input type="text" name="part-${partIndex}-option-${newOptionIndex}-text"
                   value="New Option ${newOptionIndex + 1}"
                   class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                   oninput="updateRadioValue(this, 'part-${partIndex}-option-${newOptionIndex}-radio')">
            <button type="button" class="text-red-600 hover:text-red-800 remove-mcq-option-btn p-1"
                    onclick="removeMcqOption(this, '${partIndex}')">
                <i class="fas fa-times"></i>
            </button>
        `;
        optionsContainer.appendChild(newOptionDiv);
        // No renumbering needed here if server can handle arbitrary indices or if names are re-calculated on renumberParts.
        // For consistency and easier server-side parsing if it expects sequential option indices, call renumber.
        renumberMcqOptions(partIndex);
    }

    // Function to remove an MCQ option
    function removeMcqOption(button, partIndex) {
        const optionItem = button.closest('.mcq-option-item');
        if (optionItem) {
            optionItem.remove();
            renumberMcqOptions(partIndex);
        }
    }

    // Function to renumber MCQ options for a specific part
    function renumberMcqOptions(partIndex) {
        partIndex = String(partIndex);
        const optionsContainer = document.getElementById(`mcq-options-container-${partIndex}`);
        if (!optionsContainer) return;

        const optionItems = optionsContainer.querySelectorAll('.mcq-option-item');
        optionItems.forEach((item, index) => {
            item.setAttribute('data-option-index', index);

            const radio = item.querySelector('input[type="radio"]');
            const textInput = item.querySelector('input[type="text"]');
            const removeBtn = item.querySelector('.remove-mcq-option-btn');

            if (radio) {
                radio.name = `part-${partIndex}-correct-answer`; // Name is common for the group
                radio.id = `part-${partIndex}-option-${index}-radio`;
                // Value is already set/updated by oninput or initial load
            }
            if (textInput) {
                textInput.name = `part-${partIndex}-option-${index}-text`;
                textInput.setAttribute('oninput', `updateRadioValue(this, 'part-${partIndex}-option-${index}-radio')`);
            }
            if (removeBtn) {
                removeBtn.setAttribute('onclick', `removeMcqOption(this, '${partIndex}')`);
            }
        });
    }

    // Extend renumberParts to handle MCQ options
    const originalRenumberParts = renumberParts;
    renumberParts = function() {
        originalRenumberParts.apply(this, arguments); // Call original function

        const allParts = document.querySelectorAll('.part-item');
        allParts.forEach((part, newPartIndex) => {
            const mcqOptionsContainer = part.querySelector('.mcq-options-container');
            if (mcqOptionsContainer) {
                mcqOptionsContainer.id = `mcq-options-container-${newPartIndex}`;
                const addOptionButton = part.querySelector('.add-mcq-option-btn');
                if (addOptionButton) {
                    addOptionButton.setAttribute('onclick', `addMcqOption('${newPartIndex}')`);
                }
                renumberMcqOptions(newPartIndex);
            }
        });
    }
</script>
