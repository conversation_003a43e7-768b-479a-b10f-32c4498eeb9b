#!/usr/bin/env python3
"""
Debug script to check database configuration and file paths
"""
import os
import sqlite3
from urllib.parse import urlparse

def debug_database_url():
    """Debug the database URL configuration"""
    print("=== Database URL Debug ===")
    
    # Get the DATABASE_URL from environment
    database_url = os.getenv('DATABASE_URL')
    print(f"Environment DATABASE_URL: {database_url}")
    
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set!")
        return
    
    # Parse the URL
    parsed = urlparse(database_url)
    print(f"Parsed URL scheme: {parsed.scheme}")
    print(f"Parsed URL path: {parsed.path}")
    
    # Check if it's SQLite
    if parsed.scheme == 'sqlite':
        # Extract the file path
        if database_url.startswith('sqlite:////'):
            # 4 slashes - this is likely the issue
            file_path = database_url[10:]  # Remove 'sqlite:///'
            print(f"WARNING: Using 4 slashes in SQLite URL!")
            print(f"Extracted file path: {file_path}")
            print(f"Correct format should be: sqlite:///{file_path}")
        elif database_url.startswith('sqlite:///'):
            # 3 slashes - correct format
            file_path = database_url[10:]  # Remove 'sqlite://'
            print(f"SQLite URL format looks correct")
            print(f"Extracted file path: {file_path}")
        else:
            print(f"Unexpected SQLite URL format: {database_url}")
            return
        
        # Check if the directory exists
        dir_path = os.path.dirname(file_path)
        print(f"Database directory: {dir_path}")
        
        if os.path.exists(dir_path):
            print(f"✓ Directory exists: {dir_path}")
            
            # Check directory permissions
            if os.access(dir_path, os.W_OK):
                print(f"✓ Directory is writable")
            else:
                print(f"✗ Directory is NOT writable")
                
        else:
            print(f"✗ Directory does NOT exist: {dir_path}")
            print("You may need to create the directory first")
        
        # Check if the database file exists
        if os.path.exists(file_path):
            print(f"✓ Database file exists: {file_path}")
            
            # Check file size
            file_size = os.path.getsize(file_path)
            print(f"Database file size: {file_size} bytes")
            
            # Try to connect and check tables
            try:
                conn = sqlite3.connect(file_path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall()]
                conn.close()
                
                if tables:
                    print(f"✓ Tables found in database: {', '.join(tables)}")
                else:
                    print("✗ No tables found in database (empty database)")
                    
            except Exception as e:
                print(f"✗ Error connecting to database: {e}")
                
        else:
            print(f"✗ Database file does NOT exist: {file_path}")
    
    else:
        print(f"Database is not SQLite (scheme: {parsed.scheme})")

def check_flask_app_config():
    """Check Flask app database configuration"""
    print("\n=== Flask App Configuration ===")
    
    try:
        from app import app
        with app.app_context():
            db_uri = app.config.get('SQLALCHEMY_DATABASE_URI')
            print(f"Flask SQLALCHEMY_DATABASE_URI: {db_uri}")
            
            # Import db to check engine
            from models import db
            print(f"SQLAlchemy engine URL: {db.engine.url}")
            
            # Check if they match
            if str(db.engine.url) == db_uri:
                print("✓ Engine URL matches config")
            else:
                print("✗ Engine URL does NOT match config!")
                print(f"  Config: {db_uri}")
                print(f"  Engine: {db.engine.url}")
                
    except Exception as e:
        print(f"Error checking Flask app config: {e}")

def suggest_fixes():
    """Suggest potential fixes"""
    print("\n=== Suggested Fixes ===")
    
    database_url = os.getenv('DATABASE_URL')
    if database_url and database_url.startswith('sqlite:////'):
        print("1. Fix the DATABASE_URL format:")
        corrected_url = database_url.replace('sqlite:////', 'sqlite:///')
        print(f"   Change from: {database_url}")
        print(f"   Change to:   {corrected_url}")
        print()
    
    print("2. Ensure the persistent disk directory exists:")
    print("   mkdir -p /var/data")
    print("   chmod 755 /var/data")
    print()
    
    print("3. Test database creation manually:")
    print("   python3 -c \"import sqlite3; conn = sqlite3.connect('/var/data/database.db'); conn.execute('CREATE TABLE test (id INTEGER)'); conn.commit(); conn.close(); print('Database created successfully')\"")
    print()
    
    print("4. Check file permissions after creation:")
    print("   ls -la /var/data/database.db")

if __name__ == "__main__":
    debug_database_url()
    check_flask_app_config()
    suggest_fixes()
