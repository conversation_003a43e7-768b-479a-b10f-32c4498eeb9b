import os
import json
import logging
from logging.handlers import RotatingFileHandler
from datetime import timedelta
from flask import Flask, session, jsonify, url_for, redirect, request, flash, render_template # Keep core Flask imports
from flask_session import Session
from flask_migrate import Migrate
from datetime import timedelta
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from dotenv import load_dotenv
import random
import string
from werkzeug.middleware.proxy_fix import ProxyFix
#hi
# Import database models and db object
# All models must be imported to ensure they are registered with SQLAlchemy
from models import (
    db, Subject, Topic, User, Question, Part, Option, Attachment,  # noqa: F401
    Group, DailyActivity, DailyActiveTime, Submission, ProblemSet,  # noqa: F401
    ProblemSetSubmission, IncompleteSubmission, MarkingPoint, Post  # noqa: F401
)

# Import clients (initialize here or pass from factory)
from groq import Groq
from mistralai import Mistral
from supabase import create_client
from gotrue.errors import AuthApiError # Import specific Supabase error type
from pinecone import Pinecone

# Import route registration functions
from routes.utils import (
    register_template_filters,
    register_context_processors,
    register_logging_hooks,
    register_error_handlers,
    register_file_serving_routes,
    update_user_activity,
    login_required,
    error_logger,
    user_logger,
    app_logger
)
from routes.core import register_core_routes
from routes.vault import register_vault_routes
from routes.admin import register_admin_routes
from routes.groups import register_group_routes
from routes.problemsets import register_problemset_routes
from routes.api import register_api_routes
from routes.onboarding import register_onboarding_routes
from routes.dashboard import register_dashboard_routes
from routes.attachment_handler import attachment_bp
from routes.file_serve import serve_bp
# from routes.ai_helpers import ai_bp # Import the new AI blueprint
from routes.ai_helpers import ai_bp

app = Flask(__name__)
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1)
# --- Logging Setup ---
# Configure logging globally first, before app configuration potentially uses it
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

loggers_config = {
    'app': {'level': logging.INFO, 'filename': 'app.log'},
    'user_activity': {'level': logging.INFO, 'filename': 'user_activity.log'},
    'errors': {'level': logging.ERROR, 'filename': 'errors.log'}
}

for name, config in loggers_config.items():
    logger = logging.getLogger(name)
    logger.setLevel(config['level'])
    logger.propagate = False # Prevent duplicate logs if root logger is configured

    # File Handler
    file_handler = RotatingFileHandler(
        os.path.join(log_dir, config['filename']),
        maxBytes=10485760, backupCount=10
    )
    file_handler.setFormatter(log_format)
    logger.addHandler(file_handler)

    # Console Handler (for 'app' and 'errors' loggers only?)
    if name in ['app', 'errors']:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(log_format)
        logger.addHandler(console_handler)

# Get logger instances for use
app_logger = logging.getLogger('app')
user_logger = logging.getLogger('user_activity')
error_logger = logging.getLogger('errors')


load_dotenv() # Load env vars needed for clients
groq_client = Groq(api_key=os.getenv("GROQ_API_KEY"))
mistral_client = Mistral(api_key=os.getenv("MISTRAL_API_KEY"))
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")
google_client_id = os.getenv("GOOGLE_CLIENT_ID")
google_client_secret = os.getenv("GOOGLE_CLIENT_SECRET")
if not supabase_url or not supabase_key:
     app_logger.critical("Supabase URL or Key missing!")
     supabase = None # Handle gracefully if possible, though auth will fail
else:
     # Initialize Supabase client with PKCE flow type for OAuth
     from supabase.client import ClientOptions
     from flask_session import Session
     from custom_storage import FileSystemStorage

     # Create custom storage for Supabase auth
     auth_storage_dir = os.path.join(app.instance_path, 'auth_storage')
     os.makedirs(auth_storage_dir, exist_ok=True)
     custom_storage = FileSystemStorage(auth_storage_dir)

     # Create Supabase client with PKCE flow and custom file storage
     supabase = create_client(
          supabase_url,
          supabase_key,
          options=ClientOptions(
               flow_type="pkce",
               storage=custom_storage
          )
     )

     # Try to restore session from storage if available
     current_session = custom_storage.get_current_session()
     if current_session:
         try:
             # Extract tokens from the stored session
             access_token = current_session.get('access_token')
             refresh_token = current_session.get('refresh_token')

             if access_token and refresh_token:
                 # Set the session in the Supabase client
                 supabase.auth.set_session(access_token, refresh_token)
                 app_logger.info("Restored Supabase session from custom storage")
         except Exception as e:
             error_logger.exception(f"Error restoring Supabase session: {str(e)}")

     app_logger.info("Supabase client initialized with PKCE flow type using custom FileSystemStorage")


os.makedirs(app.instance_path, exist_ok=True)
default_db_path = 'sqlite:///' + os.path.join(app.instance_path, 'database.db')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', default_db_path) # Use env var, default to sqlite in instance folder
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
# Keep uploads outside instance folder for easier access/management? Or move inside? Let's keep it outside for now.
app.config['UPLOAD_FOLDER'] = os.getenv('UPLOAD_FOLDER', os.path.join(os.getcwd(), 'uploads'))
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024 # 50MB upload limit
# Session cookie settings - adjust for development vs production
app.config['SESSION_COOKIE_SECURE'] = os.getenv('FLASK_ENV') == 'production'  # Only require HTTPS in production
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# Secret Key (essential for session security)
secret_key_path = os.path.join(app.instance_path, 'secret_key.txt') # Store in instance folder
os.makedirs(app.instance_path, exist_ok=True) # Ensure instance folder exists
if os.path.exists(secret_key_path):
    with open(secret_key_path, 'rb') as f:
        app.config['SECRET_KEY'] = f.read()
else:
    app.config['SECRET_KEY'] = os.urandom(24)
    with open(secret_key_path, 'wb') as f:
        f.write(app.config['SECRET_KEY'])
    app_logger.info(f"Generated new secret key at {secret_key_path}")

# Session Configuration
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)
app.config['SESSION_TYPE'] = 'filesystem' # Consider 'redis' for scalability
app.config['SESSION_FILE_DIR'] = os.path.join(app.instance_path, 'flask_session') # Use instance folder
app.config['SESSION_USE_SIGNER'] = True  # Add cryptographic signing for session cookies
app.config['SESSION_PERMANENT'] = True  # Make all sessions permanent by default
os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

# Ensure upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# --- Initialize Extensions ---
db.init_app(app)
Session(app)
migrate = Migrate(app, db) # Keep migrate initialization

# --- Rate Limiter Setup ---
def get_user_id_or_ip():
    """Key function for rate limiter: uses user_id if logged in, otherwise IP."""
    if 'user_id' in session:
        return str(session['user_id'])
    return get_remote_address()

limiter = Limiter(
    get_user_id_or_ip,
    app=app,
    storage_uri=os.getenv('REDIS_URL', "memory://"), # Use Redis if available, else memory
    strategy="fixed-window" # or "moving-window"
)

# --- Register Blueprints ---
app.register_blueprint(attachment_bp)
app.register_blueprint(serve_bp, url_prefix='/serve')
app.register_blueprint(ai_bp)

# --- Register Hooks, Filters, Processors, Error Handlers ---
register_template_filters(app)
register_context_processors(app)
register_logging_hooks(app)
register_error_handlers(app, limiter)

# --- Register Routes ---
register_file_serving_routes(app) # From utils

# --- Core Routes (Login, Register, Callback, Logout) ---
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build

os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

client_config = {
    "web": {
        "client_id": google_client_id,
        "client_secret": google_client_secret,
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "redirect_uris": ["http://localhost:8000/callback", "https://vast-stlm.onrender.com/callback"]
    }
}

def create_flow():
    flow = Flow.from_client_config(
        client_config,
        scopes=["https://www.googleapis.com/auth/userinfo.profile", "https://www.googleapis.com/auth/userinfo.email", "openid"],
        redirect_uri="http://localhost:8000/callback" if os.getenv('FLASK_ENV') != 'production' else "https://vast-stlm.onrender.com/callback"
    )
    return flow

@app.route("/login_google", methods=['GET', 'POST'])
def login_google():
    """Initiate Google OAuth login flow"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    # Check if this is coming from register page
    source = request.args.get('source', 'login')

    flow = create_flow()
    authorization_url, state = flow.authorization_url(
        access_type='offline',
        include_granted_scopes='true',
        prompt='select_account'  # Force account selection page
    )
    session['state'] = state
    session['oauth_source'] = source  # Remember where the OAuth was initiated from
    session.permanent = True  # Ensure session persists

    app_logger.info(f"OAuth initiated from {source} - Generated state: {state}, Session ID: {session.get('_id', 'No ID')}")

    return redirect(authorization_url)

@app.route("/callback")
def callback():
    """Handle Google OAuth callback and login/register user"""
    # Debug logging for state parameter issue
    received_state = request.args.get('state')
    session_state = session.get('state')

    app_logger.info(f"OAuth callback - Received state: {received_state}, Session state: {session_state}")

    if received_state != session_state:
        error_logger.warning(f"OAuth state mismatch - Received: {received_state}, Expected: {session_state}")

        # In development, be more lenient with state validation
        if os.getenv('FLASK_ENV') != 'production' and received_state:
            app_logger.warning("Development mode: Proceeding despite state mismatch")
        else:
            flash('Authentication failed due to security check. Please try again.', 'error')
            return redirect(url_for('login'))

    flow = create_flow()
    flow.fetch_token(authorization_response=request.url)

    # Get user info from Google
    credentials = flow.credentials
    service = build('oauth2', 'v2', credentials=credentials)
    user_info = service.userinfo().get().execute()

    try:
        google_email = user_info.get('email')
        google_name = user_info.get('name', '')
        oauth_source = session.get('oauth_source', 'login')

        if not google_email:
            flash('Unable to get email from Google. Please try again.', 'error')
            return redirect(url_for('login'))

        # Check if user already exists by email
        user = User.query.filter_by(email=google_email).first()

        if user:
            # Existing user - log them in (regardless of source)
            session['user_id'] = user.id
            session['username'] = user.username
            session.permanent = True

            # Check if this is the first login of the day
            first_login_of_day = user.is_first_login_of_day()
            if first_login_of_day:
                session['show_confetti'] = True
                user.update_login_date()
                db.session.commit()
                app_logger.info(f"First login of the day for user {user.username}")

            update_user_activity(user.id)
            user_logger.info(f"User {user.username} logged in successfully (Google OAuth from {oauth_source})")
            flash('Login successful!', 'success')

            # Check if user needs onboarding
            if not (user.onboarding_completed is True):
                return redirect(url_for('onboarding'))

            # Redirect to intended page or vault
            next_page = request.args.get('next')
            if next_page and next_page.startswith('/'):
                return redirect(next_page)
            return redirect(url_for('vault'))

        else:
            # New user - behavior depends on source
            if oauth_source == 'register':
                # Coming from register page - store Google info in session and redirect to username selection
                session['google_signup_data'] = {
                    'email': google_email,
                    'name': google_name,
                    'temp_user': True
                }
                session.permanent = True

                app_logger.info(f"New Google user {google_email} redirected to username selection")
                flash("Welcome! Please choose a username to complete your account setup.", "info")
                return redirect(url_for('choose_username'))
            else:
                # Coming from login page - user doesn't exist, redirect to register
                flash(f'No account found for {google_email}. Please register first or use a different email.', 'warning')
                return redirect(url_for('register'))

    except Exception as e:
        db.session.rollback()
        error_logger.exception(f"Error during Google OAuth callback: {str(e)}")
        flash("An error occurred during Google login. Please try again.", "error")
        return redirect(url_for('login'))

    finally:
        # Clean up session state
        session.pop('state', None)
        session.pop('oauth_source', None)


@app.route("/choose_username", methods=['GET', 'POST'])
def choose_username():
    """Allow new Google users to choose their username"""
    # Check if user has Google signup data in session
    google_data = session.get('google_signup_data')
    if not google_data or not google_data.get('temp_user'):
        flash('Invalid access. Please sign up again.', 'error')
        return redirect(url_for('register'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()

        if not username:
            flash('Username is required.', 'warning')
            return render_template('choose_username.html', google_data=google_data)

        # Validate username (basic validation)
        if len(username) < 3:
            flash('Username must be at least 3 characters long.', 'warning')
            return render_template('choose_username.html', google_data=google_data)

        if not username.replace('_', '').replace('-', '').isalnum():
            flash('Username can only contain letters, numbers, hyphens, and underscores.', 'warning')
            return render_template('choose_username.html', google_data=google_data)

        # Check if username is already taken
        if User.query.filter_by(username=username).first():
            flash('Username is already taken. Please choose a different one.', 'warning')
            return render_template('choose_username.html', google_data=google_data)

        try:
            # Create the user account
            user = User(username=username, email=google_data['email'])
            # Set a random password (user won't use it since they'll login via Google)
            import secrets
            random_password = secrets.token_urlsafe(32)
            user.set_password(random_password)
            user.role = 'member'  # Default role

            db.session.add(user)
            db.session.commit()

            # Log the new user registration
            user_logger.info(f"New user registered via Google OAuth: {username} ({google_data['email']})")

            # Auto-login the new user
            session['user_id'] = user.id
            session['username'] = user.username
            session.permanent = True

            # Clear the temporary Google data
            session.pop('google_signup_data', None)

            # Update user activity
            update_user_activity(user.id)

            flash("Welcome to Vast! Your account has been created. Let's set up your profile.", "success")
            return redirect(url_for('onboarding'))

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error creating Google user account: {str(e)}")
            flash("An error occurred while creating your account. Please try again.", "error")
            return render_template('choose_username.html', google_data=google_data)

    # GET request - show username selection form
    # Generate a suggested username from Google name or email
    suggested_username = ""
    if google_data.get('name'):
        suggested_username = google_data['name'].replace(' ', '').lower()
    else:
        suggested_username = google_data['email'].split('@')[0]

    # Make sure suggested username is unique
    if User.query.filter_by(username=suggested_username).first():
        counter = 1
        base_username = suggested_username
        while User.query.filter_by(username=f"{base_username}{counter}").first():
            counter += 1
        suggested_username = f"{base_username}{counter}"

    return render_template('choose_username.html',
                         google_data=google_data,
                         suggested_username=suggested_username)

@app.route("/login", methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username_or_email = request.form.get('username')  # Keep the form field name as 'username' for backward compatibility
        password = request.form.get('password')

        if not username_or_email or not password:
            flash('Username/email and password are required.', 'warning')
            return render_template('login.html')

        # Try to find user by username first
        user = User.query.filter_by(username=username_or_email).first()

        # If not found by username, try by email
        if not user:
            user = User.query.filter_by(email=username_or_email).first()

        if user and user.check_password(password):
            # Correct login
            session['user_id'] = user.id
            session['username'] = user.username
            session.permanent = True

            # Check if this is the first login of the day
            first_login_of_day = user.is_first_login_of_day()
            if first_login_of_day:
                session['show_confetti'] = True
                user.update_login_date()
                db.session.commit()
                app_logger.info(f"First login of the day for user {user.username}")

            update_user_activity(user.id)
            user_logger.info(f"User {user.username} logged in successfully (standard login)")
            flash('Login successful!', 'success')

            # Check if user needs onboarding
            if not (user.onboarding_completed is True):
                return redirect(url_for('onboarding'))

            # Redirect to intended page or vault
            next_page = request.args.get('next')
            if next_page and next_page.startswith('/'):
                return redirect(next_page)
            return redirect(url_for('vault'))
        else:
            # Incorrect login
            if user:
                error_logger.warning(f"Failed login attempt for user {username_or_email} - incorrect password")
            else:
                error_logger.warning(f"Failed login attempt for non-existent user {username_or_email}")
            flash('Invalid username/email or password.', 'error')
            return render_template('login.html'), 401

    # GET request
    return render_template('login.html')


@app.route("/register", methods=['GET', 'POST'])
def register():
    if 'user_id' in session:
        return redirect(url_for('vault'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        admin_code = request.form.get('admin_code')

        if not all([username, email, password]):
             flash("Username, email, and password are required.", "warning")
             return redirect(url_for('register'))

        # Check for existing user/email
        if User.query.filter_by(email=email).first():
            error_logger.info(f"Registration attempt with existing email: {email}")
            flash("Email address is already registered. Please log in or use a different email.", "warning")
            return redirect(url_for('register'))

        # Check for existing username locally *before* hitting Supabase, as Supabase only checks email uniqueness by default
        if User.query.filter_by(username=username).first():
            error_logger.info(f"Registration attempt with existing username: {username}")
            flash("Username is already taken. Please choose a different username.", "warning")
            return redirect(url_for('register'))

        try:
            # Step 1: Attempt to sign up user in Supabase Auth
            app_logger.info(f"Attempting Supabase sign up for email: {email}")
            sign_up_response = supabase.auth.sign_up({
                "email": email,
                "password": password,
            })
            app_logger.info(f"Supabase sign up response received for {email}") # Don't log the full response object directly if it contains sensitive info
            supabase_user = sign_up_response.user

            if not supabase_user:
                 # This case might indicate an issue even if no exception was raised, or if email confirmation is pending
                 # Depending on Supabase settings (e.g., require email confirmation), supabase_user might be None initially.
                 # If email confirmation is ON, the user exists in Supabase but isn't fully active yet.
                 app_logger.warning(f"Supabase sign up for {email} did not return a user object immediately (may require email confirmation). Proceeding with local user creation.")
                 # If email confirmation is required, the user won't be able to log in until confirmed.

            # Step 2: If Supabase sign up didn't raise an exception, create local user
            app_logger.info(f"Creating local user record for {username} ({email})")
            # We still create a local user object, but password handling might differ
            user = User(username=username, email=email)

            user.set_password(password)

            correct_admin_code = os.getenv('ADMIN_REGISTRATION_CODE', 'vastadmin2024')
            if admin_code and admin_code == correct_admin_code:
                user.role = 'admin'
                app_logger.info(f"New admin user registered: {username}")
            else:
                user.role = 'member' # Default role
                app_logger.info(f"New member user registered: {username}")
                if admin_code: # Log if an incorrect code was attempted
                     error_logger.warning(f"Incorrect admin code provided during registration for user {username}")

            db.session.add(user)
            db.session.commit() # Commit local user

            # Log success for both systems (or indicate if Supabase confirmation is pending)
            log_message = f"User {username} ({email}) registered."
            if supabase_user:
                 log_message += f" Supabase User ID: {supabase_user.id}, Role: {user.role}"
            else:
                 log_message += f" Local Role: {user.role}. Supabase user requires confirmation or sign up response was unexpected."
            user_logger.info(log_message)

            # Auto-login the user after successful registration
            session['user_id'] = user.id
            session['username'] = user.username
            session.permanent = True

            # Update user activity
            update_user_activity(user.id)

            flash("Registration successful! Let's set up your profile.", "success")
            return redirect(url_for('onboarding'))

        except AuthApiError as e:
            # Handle specific Supabase errors (e.g., user already exists)
            db.session.rollback() # Rollback local DB changes if Supabase fails
            error_logger.error(f"Supabase AuthApiError during registration for {email}: Status={e.status}, Message={e.message}")
            if "User already registered" in e.message:
                 flash("This email address is already registered. Please log in or use the forgot password link.", "warning")
            else:
                 flash(f"An authentication error occurred: {e.message}", "error")
            return redirect(url_for('register'))

        except Exception as e:
             db.session.rollback()
             error_logger.exception(f"Unexpected error during user registration for {username}: {str(e)}")
             flash("An unexpected error occurred during registration. Please try again.", "error")
             return redirect(url_for('register'))

    # GET request
    return render_template("register.html")

@app.route("/cancel_google_signup")
def cancel_google_signup():
    """Cancel Google signup process and clear session data"""
    session.pop('google_signup_data', None)
    session.pop('state', None)
    session.pop('oauth_source', None)
    flash('Google signup cancelled.', 'info')
    return redirect(url_for('register'))

@app.route("/logout")
@login_required # Ensure user is logged in to log out
def logout():
    user_id = session.get('user_id')
    username = session.get('username', 'Unknown')

    # Optional: Update activity one last time before logout
    if user_id:
         update_user_activity(user_id)

    try:
        # Sign out from Supabase if possible
        supabase.auth.sign_out()

        # Clear the custom storage
        if 'custom_storage' in globals():
            custom_storage.remove_item("current_session")
            app_logger.info("Cleared current session from custom storage")

        app_logger.info(f"User {username} signed out from Supabase")
    except Exception as e:
        error_logger.warning(f"Error signing out from Supabase: {str(e)}")

    # Clear the session
    session.pop('user_id', None)
    session.pop('username', None)
    session.pop('oauth_state', None)
    # session.clear() # Use clear() if you want to remove everything

    user_logger.info(f"User {username} (ID: {user_id}) logged out")
    flash("You have been logged out.", "info")

    # Redirect to index or login page
    # Using request.referrer can be unreliable/insecure, prefer fixed redirect
    return redirect(url_for('login'))

@app.route("/delete_account", methods=['POST'])
@login_required
def delete_account():
    """Deletes a user's account from both local database and Supabase."""
    user_id = session.get('user_id')
    username = session.get('username', 'Unknown')

    if not user_id:
        flash("You must be logged in to delete your account.", "error")
        return redirect(url_for('login'))

    # Get the user from the database
    user = User.query.get(user_id)
    if not user:
        flash("User not found.", "error")
        return redirect(url_for('login'))

    try:
        # First, try to delete the user from Supabase
        # Note: This requires an active session in Supabase
        try:
            # The user needs to be signed in to delete their account in Supabase
            # We'll attempt to use the current session
            response = supabase.auth.get_user()
            print(response)
            supabase_user_id = response.user.id
            app_logger.info(f"Attempting to delete user ID {supabase_user_id} in Supabase")
            url = os.getenv("SUPABASE_URL")
            service_key = os.getenv("SUPABASE_SERVICE_KEY")
            supabase_admin = create_client(url, service_key)
            supabase_admin.auth.admin.delete_user(
                supabase_user_id
            )
            app_logger.info(f"User {username} (ID: {user_id}, marked as deleted in Supabase")
        except Exception as e:
            # Log the error but continue with local deletion
            error_logger.error(f"Error updating user in Supabase: {str(e)}")
            # We'll still delete the local user even if Supabase update fails

        # Delete related data (submissions, activities, etc.)
        # Explicitly delete related records to ensure clean deletion
        from models import Submission, DailyActivity, DailyActiveTime, ProblemSetSubmission, IncompleteSubmission

        # Delete user's submissions
        Submission.query.filter_by(user_id=user_id).delete()

        # Delete user's daily activity records
        DailyActivity.query.filter_by(user_id=user_id).delete()

        # Delete user's daily active time records
        DailyActiveTime.query.filter_by(user_id=user_id).delete()

        # Delete user's problem set submissions
        ProblemSetSubmission.query.filter_by(user_id=user_id).delete()

        # Delete user's incomplete submissions
        IncompleteSubmission.query.filter_by(user_id=user_id).delete()

        # Handle groups owned by the user
        # Option 1: Delete groups owned by the user
        for group in user.owned_groups:
            db.session.delete(group)

        # Option 2: Transfer ownership to another user (not implemented here)

        # Remove user from all groups they're a member of
        user.groups = []

        # Delete the user from the local database
        db.session.delete(user)
        db.session.commit()

        # Clear the session
        session.pop('user_id', None)
        session.pop('username', None)

        user_logger.info(f"User {username} (ID: {user_id}) account deleted")
        flash("Your account has been successfully deleted.", "success")

        return redirect(url_for('login'))

    except Exception as e:
        db.session.rollback()
        error_logger.exception(f"Error deleting user account {user_id}: {str(e)}")
        flash(f"An error occurred while deleting your account: {str(e)}", "error")
        return redirect(url_for('user_profile', username=username))



@app.route('/forgot_password', methods=['GET', 'POST'])
def forgot_password():
    if request.method == 'POST':
        email = request.form.get('email')

        if not email:
            flash('Email address is required.', 'warning')
            return render_template('forgot_password.html')

        # Check if user exists locally (optional but good practice)
        user = User.query.filter_by(email=email).first()
        if not user:
            # Don't reveal if email exists, just show generic message
            app_logger.warning(f"Password reset requested for non-existent email: {email}")
            flash('If an account exists with this email, an OTP will be sent.', 'info')
            return redirect(url_for('login'))

        try:
            # Send OTP to user's email
            supabase.auth.sign_in_with_otp({
                'email': email,
                'options': {
                    'should_create_user': False,
                }
            })

            app_logger.info(f"OTP sent to {email} for password reset")
            flash('A one-time password has been sent to your email.', 'success')
            return redirect(url_for('reset_password', email=email))
        except Exception as e:
            error_logger.exception(f"Error sending OTP: {str(e)}")
            flash('An error occurred. Please try again later.', 'error')
            return render_template('forgot_password.html')

    return render_template('forgot_password.html')


@app.route('/reset_password', methods=['GET', 'POST'])
def reset_password():
    """
    Handles password reset using OTP.
    GET: Renders the form, potentially pre-filling email.
    POST: Verifies OTP and updates password.
    """
    app_logger.info(f"Reset Password Route Accessed (OTP Flow). Method: {request.method}")

    if request.method == 'GET':
        email = request.args.get('email')
        app_logger.info(f"Rendering reset_password template for GET request. Email prefill: {email}")
        return render_template('reset_password.html', email=email)

    if request.method == 'POST':
        app_logger.info("Processing POST request for password reset (OTP Flow).")
        email = request.form.get('email')
        submitted_otp = request.form.get('otp')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        # Basic validation
        if not all([email, submitted_otp, new_password, confirm_password]):
            flash('All fields are required.', 'warning')
            return render_template('reset_password.html', email=email)

        if new_password != confirm_password:
            flash('Passwords do not match.', 'warning')
            return render_template('reset_password.html', email=email)

        try:
            # Verify OTP - this should establish a session
            supabase.auth.verify_otp({
                'email': email,
                'token': submitted_otp,
                'type': 'email'
            })

            # Now we have a valid session, update the password
            supabase.auth.update_user({
                'password': new_password,
            })

            # Update local user password as well
            user = User.query.filter_by(email=email).first()
            if user:
                user.set_password(new_password)
                db.session.commit()

            flash('Password reset successful. Please log in using your new password.', 'success')
            return redirect(url_for('login'))
        except Exception as e:
            error_logger.exception(f"Error during password reset: {str(e)}")
            flash('An error occurred during password reset. Please try again.', 'error')
            return render_template('reset_password.html', email=email)


# --- Register Other Route Blueprints/Functions ---
register_core_routes(app, db, session)
register_vault_routes(app, db, session)
register_admin_routes(app, db, session)
register_group_routes(app, db, session)
register_problemset_routes(app, db, session)
register_onboarding_routes(app, db, session)
register_dashboard_routes(app, db, session)
register_api_routes(app, db, session, limiter, groq_client, mistral_client)

# Route to clear the confetti flag
@app.route('/clear_confetti', methods=['POST'])
def clear_confetti():
    if 'show_confetti' in session:
        session.pop('show_confetti')
    return jsonify({'status': 'success'})

# API endpoint for checking username availability
@app.route('/api/check_username', methods=['POST'])
def check_username():
    """Check if a username is available"""
    data = request.get_json()
    if not data or 'username' not in data:
        return jsonify({'error': 'Username is required'}), 400

    username = data['username'].strip()

    # Basic validation
    if len(username) < 3:
        return jsonify({'available': False, 'reason': 'Username must be at least 3 characters long'})

    if not username.replace('_', '').replace('-', '').isalnum():
        return jsonify({'available': False, 'reason': 'Username can only contain letters, numbers, hyphens, and underscores'})

    # Check if username exists
    existing_user = User.query.filter_by(username=username).first()

    return jsonify({
        'available': existing_user is None,
        'username': username
    })

# Debug route for OAuth issues (development only)
@app.route('/debug/session')
def debug_session():
    if os.getenv('FLASK_ENV') == 'production':
        return "Not available in production", 404

    session_data = dict(session)
    return jsonify({
        'session_data': session_data,
        'session_id': session.get('_id', 'No ID'),
        'permanent': session.permanent
    })



# --- CLI Commands (Moved from create_app) ---
@app.cli.command('seed')
def init_db():
     '''
     Initialise DB with subject/topic data
     '''
     with open('content/subject_data.json', 'r') as f:
         data = json.load(f)

     subject_map = {}
     for subject_data in data['subjects']:
         subject = Subject.query.filter_by(syllabus=subject_data['syllabus']).first()
         if not subject:
             subject = Subject(name=subject_data['name'], syllabus=subject_data['syllabus'])
             db.session.add(subject)
             db.session.commit() # creates primary key
         subject_map[subject.name] = subject.id

     for topic_data in data['topics']:
         subject_id = subject_map.get(topic_data['subject'])
         if subject_id:
             topic = Topic.query.filter_by(name=topic_data['name']).first()
             if not topic:
                 topic = Topic(name=topic_data['name'], subject_id=subject_id)
                 db.session.add(topic)

     db.session.commit()

     print("DB init done")


if __name__ == "__main__":

    # Use environment variables for host, port, debug
    host = os.getenv('FLASK_RUN_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_RUN_PORT', 8000)) # Changed default port to 8000
    debug = os.getenv('FLASK_DEBUG', 'True').lower() in ['true', '1', 't']

    # Create database tables if they don't exist (useful for initial setup)
    # Consider using Flask-Migrate for production environments instead
    with app.app_context():
        try:
            db.create_all()
            logging.getLogger('app').info("Database tables checked/created.")

        except Exception as e:
            logging.getLogger('app').error(f"Error during initial db.create_all(): {e}")

    app.run(host=host, port=port, debug=debug, threaded=True) # Threaded might be needed for some extensions/requests
